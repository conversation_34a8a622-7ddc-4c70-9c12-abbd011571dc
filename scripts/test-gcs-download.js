const { Storage } = require('@google-cloud/storage');
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

// Initialize Google Cloud Storage
const storage = new Storage({
  projectId: process.env.GCP_PROJECT_ID,
  credentials: {
    client_email: process.env.GCP_CLIENT_EMAIL,
    private_key: process.env.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  },
});

// Function to download file from Google Cloud Storage
async function downloadFileFromGCS(gsUrl) {
  try {
    // Parse the gs:// URL
    const urlParts = gsUrl.replace('gs://', '').split('/');
    const bucketName = urlParts[0];
    const fileName = urlParts.slice(1).join('/');
    
    console.log(`📥 Downloading file from GCS: ${bucketName}/${fileName}`);
    
    // Create local downloads directory if it doesn't exist
    const downloadsDir = path.join(process.cwd(), 'downloads');
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }
    
    // Generate local file path
    const localFileName = path.basename(fileName);
    const localFilePath = path.join(downloadsDir, localFileName);
    
    // Download the file
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(fileName);
    
    await file.download({ destination: localFilePath });
    
    console.log(`✅ File downloaded successfully to: ${localFilePath}`);
    return localFilePath;
    
  } catch (error) {
    console.error(`❌ Failed to download file from GCS: ${error}`);
    throw error;
  }
}

// Test the download function
async function testDownload() {
  const testUrl = 'gs://agentq/test-data/automation-files/f907b2b1-4347-480c-8bc5-0b669649599a/70110615-4840-4fda-902d-197efffe50aa/step-2/70110615-4840-4fda-902d-197efffe50aa-step-2-1754731056440-Screenshot 2025-08-07 at 20.40.25.png';
  
  try {
    const localPath = await downloadFileFromGCS(testUrl);
    console.log(`🎉 Test successful! File available at: ${localPath}`);
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testDownload();
}

module.exports = { downloadFileFromGCS };
