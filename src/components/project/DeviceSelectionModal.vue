<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import axios from 'axios';

const props = defineProps<{
  show: boolean;
}>();

const emit = defineEmits<{
  'close': [];
  'select': [device: any];
}>();

const devices = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

const fetchDevices = async () => {
  try {
    loading.value = true;
    error.value = null;

    console.log('Fetching devices through backend proxy...');

    // Use backend API as proxy to hide device farm credentials
    const backendUrl = (import.meta as any).env.VITE_BACKEND_URL || 'http://localhost:3010';

    // Call backend endpoint that will handle device farm authentication internally
    const response = await axios.get(`${backendUrl}/device-farm/devices`);

    console.log('Devices response:', response.data);
    devices.value = response.data || [];

  } catch (error: any) {
    console.error('Failed to fetch devices:', error);
    console.error('Device fetch error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });

    if (error.response?.status === 401) {
      error.value = 'Authentication failed. Please check your permissions.';
    } else if (error.response?.status === 503) {
      error.value = 'Device farm is currently unavailable. Please try again later.';
    } else {
      error.value = error.response?.data?.message || error.message || 'Failed to fetch devices';
    }
    devices.value = [];
  } finally {
    loading.value = false;
  }
};

const selectDevice = (device: any) => {
  // Map the device properties to match what the parent component expects
  const mappedDevice = {
    id: device.id,
    udid: device.udid,
    name: device.name,
    deviceName: device.name,
    platform: device.platform,
    platformName: device.platform,
    version: device.sdk,
    platformVersion: device.sdk,
    model: device.productModel || device.deviceType,
    deviceModel: device.productModel || device.deviceType,
    state: device.state,
    status: device.isAvailable && !device.busy && !device.offline ? 'available' : 'busy',
    isAvailable: device.isAvailable,
    busy: device.busy,
    offline: device.offline,
    deviceType: device.deviceType,
    realDevice: device.realDevice,
    host: device.host
  };

  console.log('Selected device:', mappedDevice);
  emit('select', mappedDevice);
};

const closeModal = () => {
  emit('close');
};

const getDeviceIcon = (device: any): string => {
  const platform = device.platform?.toLowerCase() || device.platformName?.toLowerCase() || '';
  const deviceType = device.deviceType?.toLowerCase() || device.type?.toLowerCase() || '';
  
  if (platform.includes('android')) {
    return '🤖';
  } else if (platform.includes('ios')) {
    return '🍎';
  } else if (deviceType.includes('tablet')) {
    return '📱';
  } else {
    return '📱';
  }
};

const getDeviceStatus = (device: any): { text: string; class: string } => {
  // Check if device is offline
  if (device.offline) {
    return { text: 'Offline', class: 'offline' };
  }

  // Check if device is busy
  if (device.busy) {
    return { text: 'Busy', class: 'busy' };
  }

  // Check if device is available
  if (device.isAvailable) {
    return { text: 'Available', class: 'available' };
  }

  // Check state
  const state = device.state?.toLowerCase() || '';
  switch (state) {
    case 'booted':
    case 'running':
      return { text: 'Running', class: 'available' };
    case 'shutdown':
    case 'shutting-down':
      return { text: 'Shutdown', class: 'available' };
    default:
      return { text: 'Unknown', class: 'unknown' };
  }
};

// Watch for show prop changes to fetch devices when modal opens
watch(() => props.show, (newShow) => {
  if (newShow) {
    console.log('Device modal opened, fetching devices...');
    fetchDevices();
  }
});

onMounted(() => {
  if (props.show) {
    fetchDevices();
  }
});
</script>

<template>
  <div v-if="show" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>📱 Select Device</h3>
        <button class="close-button" @click="closeModal">×</button>
      </div>
      
      <div class="modal-body">
        <div v-if="loading" class="loading">
          <div class="loading-spinner"></div>
          <p>Loading devices...</p>
        </div>
        
        <div v-else-if="error" class="error">
          <div class="error-icon">❌</div>
          <h4>Failed to Load Devices</h4>
          <p>{{ error }}</p>
          <button class="retry-button" @click="fetchDevices">
            🔄 Retry
          </button>
        </div>
        
        <div v-else-if="devices.length === 0" class="no-devices">
          <div class="no-devices-icon">📱</div>
          <h4>No Devices Available</h4>
          <p>No devices are currently available in the device farm.</p>
          <button class="refresh-button" @click="fetchDevices">
            🔄 Refresh
          </button>
        </div>
        
        <div v-else class="devices-section">
          <div class="devices-header">
            <h4>Available Devices</h4>
            <p>{{ devices.length }} device(s) found</p>
            <button class="refresh-button small" @click="fetchDevices">
              🔄 Refresh
            </button>
          </div>
          
          <div class="devices-list">
            <div
              v-for="device in devices"
              :key="device.id || device.udid"
              class="device-item"
              :class="{ 'disabled': !device.isAvailable || device.busy || device.offline }"
              @click="(device.isAvailable && !device.busy && !device.offline) ? selectDevice(device) : null"
            >
              <div class="device-icon">{{ getDeviceIcon(device) }}</div>
              <div class="device-info">
                <div class="device-name">{{ device.name || 'Unknown Device' }}</div>
                <div class="device-details">
                  <span class="device-platform">{{ device.platform || 'Unknown' }}</span>
                  <span class="device-version">SDK {{ device.sdk || 'N/A' }}</span>
                  <span class="device-model">{{ device.productModel || device.deviceType || '' }}</span>
                  <span v-if="device.realDevice" class="device-type real">Real Device</span>
                  <span v-else class="device-type simulator">Simulator</span>
                </div>
              </div>
              <div class="device-status">
                <span class="status-badge" :class="getDeviceStatus(device).class">
                  {{ getDeviceStatus(device).text }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 700px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  
  h3 {
    margin: 0;
    font-size: 20px;
    color: #1f2937;
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  
  &:hover {
    color: #374151;
  }
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }
  
  p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error, .no-devices {
  text-align: center;
  padding: 40px 20px;
  
  .error-icon, .no-devices-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  h4 {
    margin: 0 0 8px;
    color: #1f2937;
    font-size: 18px;
  }
  
  p {
    margin: 0 0 20px;
    color: #6b7280;
    line-height: 1.5;
  }
}

.retry-button, .refresh-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-1px);
  }
  
  &.small {
    padding: 6px 12px;
    font-size: 12px;
  }
}

.devices-section {
  .devices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
      font-size: 16px;
      color: #1f2937;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.devices-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(.disabled) {
    border-color: #3b82f6;
    background: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }
  
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f9fafb;
  }
}

.device-icon {
  font-size: 24px;
  margin-right: 12px;
  width: 32px;
  text-align: center;
}

.device-info {
  flex: 1;
  
  .device-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 4px;
  }
  
  .device-details {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #6b7280;
    
    span {
      background: #f3f4f6;
      padding: 2px 6px;
      border-radius: 4px;

      &.device-type {
        font-weight: 500;

        &.real {
          background: #dbeafe;
          color: #1e40af;
        }

        &.simulator {
          background: #f3e8ff;
          color: #7c3aed;
        }
      }
    }
  }
}

.device-status {
  .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    
    &.available {
      background: #f0fdf4;
      color: #166534;
    }
    
    &.busy {
      background: #fef3c7;
      color: #92400e;
    }
    
    &.offline {
      background: #fee2e2;
      color: #991b1b;
    }
    
    &.unknown {
      background: #f3f4f6;
      color: #6b7280;
    }
  }
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .modal-body {
    padding: 16px;
  }
  
  .device-item {
    padding: 12px;
  }
  
  .device-details {
    flex-direction: column;
    gap: 4px !important;
  }
  
  .devices-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
