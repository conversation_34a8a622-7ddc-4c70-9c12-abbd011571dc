<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import axios from 'axios';
import ConfirmationModal from '../common/ConfirmationModal.vue';

const props = defineProps<{
  show: boolean;
  projectId: string;
  tcId: string;
}>();

const emit = defineEmits<{
  'close': [];
  'select': [app: any];
}>();

const mobileApps = ref<any[]>([]);
const loading = ref(false);
const uploading = ref(false);
const uploadError = ref<string | null>(null);
const showUploadForm = ref(false);

// Confirmation modal state
const showDeleteConfirmation = ref(false);
const appToDelete = ref<any>(null);

// Upload form data
const uploadForm = ref({
  file: null as File | null,
  platform: '',
  version: '',
  packageName: '',
  description: ''
});

const fetchMobileApps = async () => {
  try {
    loading.value = true;
    const fetchUrl = `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${props.projectId}/test-cases/automation-mobile/upload-mobile-app`;
    console.log('Fetching mobile apps from URL:', fetchUrl);

    const response = await axios.get(fetchUrl);
    console.log('Fetch response data:', response.data || []);
    console.log('Number of apps found:', response.data?.length || 0);

    if (response.data && Array.isArray(response.data)) {
      console.log('First app structure:', response.data[0]);
    }

    mobileApps.value = response.data || [];
    console.log('Mobile apps updated:', mobileApps.value.length, 'apps');
  } catch (error: any) {
    console.error('Failed to fetch mobile apps:', error);
    console.error('Fetch error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });
    mobileApps.value = [];
  } finally {
    loading.value = false;
  }
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;

  if (!files || files.length === 0) return;

  const file = files[0];

  // Validate file type (common mobile app formats)
  const allowedTypes = ['.apk', '.ipa', '.aab', '.app'];
  const fileName = file.name.toLowerCase();

  // Check if file has any of the allowed extensions
  // For .app files, they might have additional suffixes like .app.2.7.1
  const hasValidExtension = allowedTypes.some(ext => {
    if (ext === '.app') {
      // For .app files, check if .app appears anywhere in the filename
      return fileName.includes('.app');
    } else {
      // For other extensions, check if the file ends with the extension
      return fileName.endsWith(ext);
    }
  });

  if (!hasValidExtension) {
    uploadError.value = 'Please select a valid mobile app file (.apk, .ipa, .aab, or .app)';
    return;
  }

  // Validate file size (max 100MB)
  const maxSize = 100 * 1024 * 1024; // 100MB
  if (file.size > maxSize) {
    uploadError.value = 'File size must be less than 100MB';
    return;
  }

  // Set the file and auto-detect platform
  uploadForm.value.file = file;

  // Determine platform based on file type
  let detectedPlatform = 'ios'; // default
  if (fileName.endsWith('.apk') || fileName.endsWith('.aab')) {
    detectedPlatform = 'android';
  } else if (fileName.includes('.app')) {
    detectedPlatform = 'ios';
  } else if (fileName.endsWith('.ipa')) {
    detectedPlatform = 'ios';
  }

  uploadForm.value.platform = detectedPlatform;

  // Try to extract version from filename
  const versionMatch = file.name.match(/v?(\d+\.\d+(?:\.\d+)?)/i);
  if (versionMatch) {
    uploadForm.value.version = versionMatch[1];
  }

  // Show the upload form
  showUploadForm.value = true;
  uploadError.value = null;
};

const submitUpload = async () => {
  if (!uploadForm.value.file || !uploadForm.value.platform) {
    uploadError.value = 'File and platform are required';
    return;
  }

  try {
    uploading.value = true;
    uploadError.value = null;

    const formData = new FormData();
    formData.append('file', uploadForm.value.file);
    formData.append('platform', uploadForm.value.platform);

    if (uploadForm.value.version) {
      formData.append('version', uploadForm.value.version);
    }
    if (uploadForm.value.packageName) {
      formData.append('packageName', uploadForm.value.packageName);
    }
    if (uploadForm.value.description) {
      formData.append('description', uploadForm.value.description);
    }

    const uploadUrl = `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${props.projectId}/test-cases/automation-mobile/upload-mobile-app`;
    console.log('Uploading to URL:', uploadUrl);
    console.log('Form data entries:');
    for (let [key, value] of formData.entries()) {
      console.log(`${key}:`, value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value);
    }

    await axios.post(uploadUrl, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });

    console.log('Upload successful!');

    // Refresh the mobile apps list
    await fetchMobileApps();

    // Reset form
    resetUploadForm();

  } catch (error: any) {
    console.error('Failed to upload mobile app:', error);
    console.error('Error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method
    });
    uploadError.value = error.response?.data?.message || error.message || 'Failed to upload mobile app';
  } finally {
    uploading.value = false;
  }
};

const resetUploadForm = () => {
  uploadForm.value = {
    file: null,
    platform: '',
    version: '',
    packageName: '',
    description: ''
  };
  showUploadForm.value = false;
  uploadError.value = null;

  // Clear file input
  const fileInput = document.getElementById('mobile-app-upload') as HTMLInputElement;
  if (fileInput) {
    fileInput.value = '';
  }
};

const cancelUpload = () => {
  resetUploadForm();
};

const deleteApp = (app: any, event: Event) => {
  // Prevent the click from bubbling up to the app selection
  event.stopPropagation();

  // Store the app to delete and show confirmation modal
  appToDelete.value = app;
  showDeleteConfirmation.value = true;
};

const confirmDelete = async () => {
  if (!appToDelete.value) return;

  try {
    const deleteUrl = `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${props.projectId}/test-cases/automation-mobile/upload-mobile-app/${appToDelete.value.id}`;
    console.log('Deleting app from URL:', deleteUrl);

    await axios.delete(deleteUrl);
    console.log('App deleted successfully');

    // Refresh the mobile apps list
    await fetchMobileApps();

    // Close confirmation modal
    showDeleteConfirmation.value = false;
    appToDelete.value = null;

  } catch (error: any) {
    console.error('Failed to delete mobile app:', error);
    console.error('Delete error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });

    // Show error message to user
    alert(`Failed to delete app: ${error.response?.data?.message || error.message || 'Unknown error'}`);

    // Close confirmation modal even on error
    showDeleteConfirmation.value = false;
    appToDelete.value = null;
  }
};

const cancelDelete = () => {
  showDeleteConfirmation.value = false;
  appToDelete.value = null;
};

const selectApp = (app: any) => {
  // Map the API response properties to what the parent component expects
  const mappedApp = {
    id: app.id,
    fileName: app.originalName, // Map originalName to fileName
    fileUrl: app.fileUrl,
    fileSize: parseInt(app.fileSize) || 0,
    platform: app.platform,
    version: app.version,
    packageName: app.packageName,
    description: app.description,
    uploadedAt: app.createdAt
  };
  emit('select', mappedApp);
};

const closeModal = () => {
  emit('close');
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getAppIcon = (fileName: string): string => {
  const lowerFileName = fileName.toLowerCase();

  if (lowerFileName.endsWith('.apk')) {
    return '🤖'; // Android
  } else if (lowerFileName.endsWith('.ipa')) {
    return '🍎'; // iOS
  } else if (lowerFileName.includes('.app')) {
    return '🍏'; // iOS App (handles .app with suffixes like .app.2.7.1)
  } else if (lowerFileName.endsWith('.aab')) {
    return '📦'; // Android App Bundle
  } else {
    return '📱'; // Default mobile app icon
  }
};

// Watch for show prop changes to fetch apps when modal opens
watch(() => props.show, (newShow) => {
  if (newShow) {
    console.log('Modal opened, fetching mobile apps...');
    fetchMobileApps();
  }
});

onMounted(() => {
  if (props.show) {
    fetchMobileApps();
  }
});
</script>

<template>
  <div v-if="show" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>📱 Select Mobile App</h3>
        <button class="close-button" @click="closeModal">×</button>
      </div>
      
      <div class="modal-body">
        <!-- Upload Section -->
        <div class="upload-section">
          <div class="upload-header">
            <h4>Upload New App</h4>
            <p>Upload a mobile app file (.apk, .ipa, .aab, or .app)</p>
          </div>

          <!-- File Selection -->
          <div v-if="!showUploadForm" class="upload-area">
            <input
              type="file"
              id="mobile-app-upload"
              accept=".apk,.ipa,.aab,.app"
              @change="handleFileSelect"
              style="display: none"
            />
            <label for="mobile-app-upload" class="upload-button">
              📤 Select Mobile App File
            </label>
          </div>

          <!-- Upload Form -->
          <div v-else class="upload-form">
            <div class="form-row">
              <div class="form-group">
                <label>Selected File:</label>
                <div class="selected-file">{{ uploadForm.file?.name }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>Platform *</label>
                <select v-model="uploadForm.platform" required>
                  <option value="">Select Platform</option>
                  <option value="android">Android</option>
                  <option value="ios">iOS</option>
                </select>
              </div>
              <div class="form-group">
                <label>Version</label>
                <input v-model="uploadForm.version" type="text" placeholder="e.g., 1.2.3" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>Package Name</label>
                <input v-model="uploadForm.packageName" type="text" placeholder="e.g., com.example.app" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group full-width">
                <label>Description</label>
                <textarea v-model="uploadForm.description" placeholder="Optional description or notes"></textarea>
              </div>
            </div>

            <div class="form-actions">
              <button
                class="upload-submit-button"
                @click="submitUpload"
                :disabled="uploading || !uploadForm.platform"
                :class="{ 'uploading': uploading }"
              >
                <span v-if="!uploading">📤 Upload App</span>
                <span v-else>⏳ Uploading...</span>
              </button>
              <button class="cancel-button" @click="cancelUpload" :disabled="uploading">
                Cancel
              </button>
            </div>
          </div>

          <div v-if="uploadError" class="upload-error">
            ❌ {{ uploadError }}
          </div>
        </div>
        
        <!-- Apps List Section -->
        <div class="apps-section">
          <div class="apps-header">
            <h4>Available Mobile Apps</h4>
            <p v-if="mobileApps.length === 0 && !loading">No mobile apps uploaded yet</p>
          </div>
          
          <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
            <p>Loading mobile apps...</p>
          </div>
          
          <div v-else class="apps-list">
            <div
              v-for="app in mobileApps"
              :key="app.id"
              class="app-item"
              @click="selectApp(app)"
            >
              <button
                class="delete-app-button"
                @click="deleteApp(app, $event)"
                title="Delete this app"
              >
                ×
              </button>
              <div class="app-icon">{{ getAppIcon(app.originalName) }}</div>
              <div class="app-info">
                <div class="app-name">{{ app.originalName }}</div>
                <div class="app-details">
                  <span class="app-size">{{ formatFileSize(parseInt(app.fileSize) || 0) }}</span>
                  <span class="app-date">{{ new Date(app.createdAt).toLocaleDateString() }}</span>
                </div>
              </div>
              <div class="select-indicator">
                <span class="select-text">Select</span>
                <span class="select-arrow">→</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <ConfirmationModal
    :is-open="showDeleteConfirmation"
    title="Delete Mobile App"
    :message="`Are you sure you want to delete '${appToDelete?.originalName}'? This action cannot be undone.`"
    @confirm="confirmDelete"
    @cancel="cancelDelete"
  />
</template>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  
  h3 {
    margin: 0;
    font-size: 20px;
    color: #1f2937;
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  
  &:hover {
    color: #374151;
  }
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.upload-section {
  margin-bottom: 32px;
  
  .upload-header {
    margin-bottom: 16px;
    
    h4 {
      margin: 0 0 4px;
      font-size: 16px;
      color: #1f2937;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #6b7280;
    }
  }
  
  .upload-area {
    margin-bottom: 12px;
  }
  
  .upload-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    
    &:hover:not(.uploading) {
      background: linear-gradient(135deg, #2563eb, #1e40af);
      transform: translateY(-1px);
    }
    
    &.uploading {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
  
  .upload-error {
    color: #ef4444;
    font-size: 14px;
    padding: 8px 12px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
  }
}

.upload-form {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;

  .form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-group {
    flex: 1;

    &.full-width {
      flex: none;
      width: 100%;
    }

    label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #374151;
      font-size: 14px;
    }

    input, select, textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s ease;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
      }
    }

    textarea {
      resize: vertical;
      min-height: 60px;
    }

    .selected-file {
      padding: 8px 12px;
      background: #e5e7eb;
      border-radius: 6px;
      font-size: 14px;
      color: #374151;
      font-weight: 500;
    }
  }

  .form-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;

    .upload-submit-button {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
      border: none;
      border-radius: 6px;
      padding: 10px 20px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      &.uploading {
        opacity: 0.8;
      }
    }

    .cancel-button {
      background: #f3f4f6;
      color: #374151;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      padding: 10px 20px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background: #e5e7eb;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

.apps-section {
  .apps-header {
    margin-bottom: 16px;
    
    h4 {
      margin: 0 0 4px;
      font-size: 16px;
      color: #1f2937;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }
  
  p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.apps-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.app-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: #3b82f6;
    background: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);

    .delete-app-button {
      opacity: 1;
    }
  }
}

.app-icon {
  font-size: 24px;
  margin-right: 12px;
  width: 32px;
  text-align: center;
}

.app-info {
  flex: 1;
  
  .app-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 4px;
  }
  
  .app-details {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #6b7280;
  }
}

.select-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
  
  .select-arrow {
    transition: transform 0.2s ease;
  }
}

.app-item:hover .select-indicator .select-arrow {
  transform: translateX(4px);
}

.delete-app-button {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: #ef4444;
  color: white;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 10;
  line-height: 1;

  &:hover {
    background: #dc2626;
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .modal-body {
    padding: 16px;
  }
  
  .app-item {
    padding: 12px;
  }
  
  .app-details {
    flex-direction: column;
    gap: 4px !important;
  }

  .upload-form .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .delete-app-button {
    opacity: 1; // Always show on mobile
  }
}
</style>
