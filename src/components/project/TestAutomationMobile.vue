<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import { TestCase } from '../../types';
import { TestWebSocketClient, type TestExecutionRequest } from '../../utils/testWebSocketClient';
import MobileAppSelectionModal from './MobileAppSelectionModal.vue';
import DeviceSelectionModal from './DeviceSelectionModal.vue';

const route = useRoute();
const projectId = route.params.id as string;
const tcId = route.params.tcId as string;
const selectedTestCase = ref<TestCase | null>(null);
const automationSteps = ref<any[]>([]);
const isEditing = ref(false);
const isRunning = ref(false);

// Mobile app selection modal state
const showMobileAppModal = ref(false);
const mobileApps = ref<any[]>([]);
const loadingMobileApps = ref(false);
const currentActionForApp = ref<any>(null);

// Device selection modal state
const showDeviceModal = ref(false);
const currentActionForDevice = ref<any>(null);

// Computed property to check if automation has valid actions
const hasValidActions = computed(() => {
  return automationSteps.value.some(step => {
    // Check if step has actions in the new format
    if (step.actions && step.actions.length > 0) {
      return step.actions.some((action: any) => action.action && action.action.trim() !== '');
    }
    // Check if step has action in the legacy format
    if (step.action && step.action.trim() !== '') {
      return true;
    }
    return false;
  });
});

const handleDragOver = (step: any) => {
  step.isDragOver = true;
};

const handleDragEnter = (step: any) => {
  step.isDragOver = true;
};

const handleDragLeave = (step: any, event: DragEvent) => {
  // Only set to false if we're leaving the drop area entirely
  const target = event.currentTarget as HTMLElement;
  const relatedTarget = event.relatedTarget as HTMLElement;
  if (!target.contains(relatedTarget)) {
    step.isDragOver = false;
  }
};

const handleDrop = (step: any, event: DragEvent) => {
  step.isDragOver = false;
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    processFile(step, files[0]);
  }
};

const handleFileSelect = (step: any, event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (files && files.length > 0) {
    processFile(step, files[0]);
  }
};

const processFile = async (step: any, file: File) => {
  step.fileError = '';
  step.uploading = true;

  // Store the file reference immediately for upload progress display
  step.selectedFile = file;

  // File validation (existing code)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    step.fileError = 'File size must be less than 10MB';
    step.uploading = false;
    step.selectedFile = null; // Clear file reference on error
    return;
  }

  try {
    // Upload file immediately to backend
    const formData = new FormData();
    formData.append('file', file);
    formData.append('stepId', step.step.toString());
    formData.append('testCaseId', selectedTestCase.value?.id || '');

    const response = await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${tcId}/automation-mobile/upload-file`,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' }
      }
    );

    // Store the uploaded file metadata
    step.value = file.name;
    step.fileUrl = response.data.fileUrl; // GCS URL
    step.fileId = response.data.fileId; // Unique file identifier

  } catch (error) {
    step.fileError = 'Failed to upload file';
    step.selectedFile = null; // Clear file reference on error
    console.error('File upload error:', error);
  } finally {
    step.uploading = false;
  }
};

const triggerFileInput = (step: any) => {
  const fileInput = document.querySelector(`[data-file-input="${step.step}"]`) as HTMLInputElement;
  if (fileInput) {
    fileInput.click();
  }
};

const removeFile = async (step: any) => {
  try {
    // If the file has been uploaded to the server, delete it from the backend
    if (step.fileId && step.fileUrl) {
      step.deleting = true;

      await axios.delete(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${tcId}/automation-file/files/${step.fileId}`
      );

      console.log('File deleted successfully from server');
    }

    // Clear the file from the UI
    step.selectedFile = null;
    step.value = '';
    step.fileError = '';
    step.fileUrl = null;
    step.fileId = null;

    const fileInput = document.querySelector(`[data-file-input="${step.step}"]`) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  } catch (error) {
    console.error('Failed to delete file from server:', error);
    step.fileError = 'Failed to delete file from server';

    // Still clear the file from UI even if server deletion fails
    step.selectedFile = null;
    step.value = '';
    step.fileUrl = null;
    step.fileId = null;

    const fileInput = document.querySelector(`[data-file-input="${step.step}"]`) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  } finally {
    step.deleting = false;
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to reconstruct selectedFile object from saved file data
const reconstructFileObject = (fileName: string) => {
  const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';

  // Determine file type based on extension
  let fileType = 'application/octet-stream';
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension)) {
    fileType = `image/${fileExtension === 'jpg' ? 'jpeg' : fileExtension}`;
  } else if (['pdf'].includes(fileExtension)) {
    fileType = 'application/pdf';
  } else if (['doc', 'docx'].includes(fileExtension)) {
    fileType = 'application/msword';
  } else if (['txt'].includes(fileExtension)) {
    fileType = 'text/plain';
  } else if (['mp4', 'avi', 'mov'].includes(fileExtension)) {
    fileType = `video/${fileExtension}`;
  } else if (['csv'].includes(fileExtension)) {
    fileType = 'text/csv';
  }

  // Create a mock File object for display purposes
  return {
    name: fileName,
    type: fileType,
    size: 0, // We don't have the original size, so set to 0
    lastModified: Date.now()
  };
};

const getFileIcon = (fileType: string): string => {
  if (fileType.startsWith('image/')) return '🖼️';
  if (fileType === 'application/pdf') return '📄';
  if (fileType.startsWith('video/')) return '🎬';
  if (fileType.includes('word')) return '📝';
  if (fileType.includes('csv')) return '📊';
  return '📄';
};

// Test execution state
const testRunning = ref(false);
const testProgress = ref({
  status: 'idle',
  logs: [] as string[],
  error: '',
  message: '',
  currentStep: 0,
  totalSteps: 0
});

// WebSocket client for test execution
let wsClient: TestWebSocketClient | null = null;

// Current client ID for this test session
let currentClientId: string | null = null;

// Generate unique client ID for this test session
const generateUniqueClientId = () => {
  return `client-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

// Generate unique action ID for multiple actions per step
const generateActionId = () => {
  return `action-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

// Utility function to convert multiple actions to execution steps
const convertToExecutionSteps = (automationSteps: any[]) => {
  const executionSteps: any[] = [];
  let stepCounter = 1;

  // If no automation steps, return empty array
  if (!automationSteps || automationSteps.length === 0) {
    console.log('No automation steps found, returning empty execution steps');
    return executionSteps;
  }

  automationSteps.forEach(step => {
    if (step.actions && step.actions.length > 0) {
      // Convert each action into a separate execution step
      step.actions.forEach((action: any, actionIndex: number) => {
        const stepName = step.actions.length > 1
          ? `${step.stepName} (Action ${actionIndex + 1})`
          : step.stepName;

        const executionStep: any = {
          step: stepCounter++,
          stepName: stepName,
          action: action.action,
          target: action.target || '',
          value: action.value || '',
          prompt: action.action === 'prompt' ? action.value : '',
          // Add metadata to track original step
          originalStep: step.step,
          actionIndex: actionIndex,
          totalActionsInStep: step.actions.length
        };

        // For upload actions, include file metadata if available
        if (action.action === 'upload') {
          // Check if file metadata is stored at step level (legacy) or action level (new)
          if (action.fileUrl && action.fileId) {
            executionStep.fileUrl = action.fileUrl;
            executionStep.fileId = action.fileId;
          } else if (step.fileUrl && step.fileId) {
            executionStep.fileUrl = step.fileUrl;
            executionStep.fileId = step.fileId;
          }
        }

        executionSteps.push(executionStep);
      });
    } else if (step.action) {
      // Handle legacy single action format
      const executionStep: any = {
        step: stepCounter++,
        stepName: step.stepName,
        action: step.action,
        target: step.target || '',
        value: step.value || '',
        prompt: step.prompt || '',
        originalStep: step.step,
        actionIndex: 0,
        totalActionsInStep: 1
      };

      // For upload actions, include file metadata if available
      if (step.action === 'upload' && step.fileUrl && step.fileId) {
        executionStep.fileUrl = step.fileUrl;
        executionStep.fileId = step.fileId;
      }

      executionSteps.push(executionStep);
    } else {
      // Step with no actions - skip it instead of creating placeholder
      console.log(`Skipping step ${step.step} (${step.stepName}) - no actions defined`);
    }
  });

  return executionSteps;
};

// AgentQ API key state
const agentqApiKey = ref<string | null>(null);

// Queue status state
const queueStatus = ref({
  active: 0,
  waiting: 0,
  total: 0,
  lastChecked: null as Date | null
});

// Create a set to track unique log messages
const loggedMessages = new Set();

// Function to clean log output for display
const cleanLogForDisplay = (log: string): string => {
  if (!log) return '';

  // Strip ANSI color codes and control characters
  let cleaned = log.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');

  // Remove progress indicators like [1A[2K
  cleaned = cleaned.replace(/\[\d+[A-Z]\[\d+[A-Z]/g, '');

  // Filter out JWT token verification logs that users don't need to see
  if (cleaned.includes('Verifying JWT token with:') ||
      cleaned.includes('JWT token verification successful')) {
    return '';
  }

  // For error messages with "Timed out", extract the important parts
  if (cleaned.includes('Timed out') && cleaned.includes('expect(') && cleaned.includes('Expected string:') && cleaned.includes('Received string:')) {
    try {
      // Extract the key information
      const locatorMatch = cleaned.match(/Locator: locator\('([^']+)'\)/);
      const expectedMatch = cleaned.match(/Expected string: "([^"]+)"/);
      const receivedMatch = cleaned.match(/Received string: "([^"]+)"/);

      if (locatorMatch && expectedMatch && receivedMatch) {
        return `Test failed: Element "${locatorMatch[1]}" has incorrect text.\n` +
               `Expected: "${expectedMatch[1]}"\n` +
               `Actual: "${receivedMatch[1]}"`;
      }
    } catch (e) {
      console.error('Error parsing error message:', e);
    }
  }

  // Remove internal object details
  if (cleaned.includes('matcherResult:') || cleaned.includes('Symbol(step)')) {
    cleaned = cleaned.split('matcherResult:')[0];
  }

  // Remove stack traces
  if (cleaned.includes('at /Users/')) {
    cleaned = cleaned.split('at /Users/')[0];
  }

  // Format the error message for better readability
  if (cleaned.includes('Timed out') && cleaned.includes('expect(')) {
    // Split by newlines and filter out noise
    const lines = cleaned.split('\n').filter(line =>
      line.trim() !== '' &&
      !line.includes('matcherResult') &&
      !line.includes('Symbol') &&
      !line.includes('stepId:') &&
      !line.includes('at Object.') &&
      !line.includes('at processTicksAndRejections')
    );

    // Join the first few meaningful lines
    cleaned = lines.slice(0, 5).join('\n');
  }

  return cleaned.trim();
};

// Modified addLogEntry function to clean logs before adding
const addLogEntry = (message: string) => {
  const cleanedMessage = cleanLogForDisplay(message);
  if (!cleanedMessage) return;
  
  if (!loggedMessages.has(cleanedMessage)) {
    loggedMessages.add(cleanedMessage);
    testProgress.value.logs.push(cleanedMessage);
  }
};

// Function to parse steps from the test case response
const parseStepsFromTestCase = (testCase: TestCase) => {
  const steps: any[] = [];

  // Add precondition as the first step if it exists
  if (testCase.precondition) {
    steps.push({
      step: 1,
      stepName: testCase.precondition.trim(),
      // Explicitly initialize clean state for new steps
      actions: [],
      fileUrl: null,
      fileId: null,
      selectedFile: null,
      fileError: '',
      uploading: false,
      isDragOver: false
    });
  }

  // Add the rest of the steps
  if (testCase.steps) {
    // Map each line to an automation step
    testCase.steps.split('\n').forEach(step => {
      // Default values with explicit clean state
      const stepObj: any = {
        step: steps.length + 1, // Start numbering after the precondition step
        stepName: step.trim(),
        // Explicitly initialize clean state for new steps
        actions: [],
        fileUrl: null,
        fileId: null,
        selectedFile: null,
        fileError: '',
        uploading: false,
        isDragOver: false
      };

      steps.push(stepObj);
    });
  }

  // Add expectation as the last step if it exists
  if (testCase.expectation) {
    const stepObj: any = {
      step: steps.length + 1,
      stepName: testCase.expectation.trim(),
      // Explicitly initialize clean state for new steps
      actions: [],
      fileUrl: null,
      fileId: null,
      selectedFile: null,
      fileError: '',
      uploading: false,
      isDragOver: false
    };

    steps.push(stepObj);
  }

  return steps;
};

// Add activeTab state to track which tab is active
const activeTab = ref('logs');

// Function to change the active tab
const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

const runTest = async () => {
  if (testRunning.value || testProgress.value.status === 'queued') {
    // Already running or queued a test, show a message
    const message = testProgress.value.status === 'queued'
      ? '⚠️ A test is already queued. Please wait for it to complete.'
      : '⚠️ A test is already running. Please wait for it to complete.';
    testProgress.value.logs.push(message);
    return;
  }

  // Check BullMQ for active jobs before starting
  testProgress.value.logs.push('🔍 Checking for active test jobs...');
  const queueStats = await checkQueueStatus();

  if (queueStats && queueStats.active >= 10) {
    const message = `⚠️ Cannot start test: ${queueStats.active} test(s) currently running. Maximum concurrent limit (10) reached.`;
    testProgress.value.logs.push(message);
    testProgress.value.logs.push(`📊 Queue status: ${queueStats.waiting} waiting, ${queueStats.active} active`);
    testProgress.value.logs.push('🚫 Test execution blocked due to concurrent test limit.');
    return;
  }

  // Check if this specific test case is already running
  const isTestCaseRunning = await checkTestCaseRunning(selectedTestCase.value?.id ?? '');
  if (isTestCaseRunning) {
    const message = `⚠️ Cannot start test: This test case is already running from another session.`;
    testProgress.value.logs.push(message);
    testProgress.value.logs.push('🚫 Test execution blocked to prevent concurrent execution of the same test case.');
    return;
  }

  if (queueStats && queueStats.waiting > 0) {
    testProgress.value.logs.push(`📋 ${queueStats.waiting} test(s) in queue. Your test will be queued.`);
  } else {
    testProgress.value.logs.push('✅ No active jobs found. Starting test...');
  }

  // Reset state for new test run
  testRunning.value = true;
  isRunning.value = true;
  testProgress.value = {
    status: 'running',
    message: 'Starting test...',
    logs: [],
    error: '',
    currentStep: 0,
    totalSteps: automationSteps.value.length
  };

  try {
    // Fetch AgentQ API key from backend
    // testProgress.value.logs.push('🔑 Fetching AgentQ API key...');
    const fetchedApiKey = await fetchAgentQApiKey();

    if (!fetchedApiKey) {
      testRunning.value = false;
      isRunning.value = false;
      testProgress.value.status = 'failed';
      testProgress.value.error = 'No AgentQ API key available';
      testProgress.value.logs.push('❌ Cannot proceed without AgentQ API key');
      return;
    }

    // Get user JWT token from localStorage for backend authentication
    const userJwtToken = localStorage.getItem('token');
    console.log('JWT Token from localStorage:', userJwtToken ? 'Found' : 'Not found');
    console.log('JWT Token length:', userJwtToken ? userJwtToken.length : 0);
    if (userJwtToken) {
      // testProgress.value.logs.push('🔐 Sending user JWT token to WebSocket for backend authentication');
    } else {
      testProgress.value.logs.push('⚠️ No user JWT token found - test may fail');
    }

    const wsUrl = (import.meta as any).env.VITE_WEBSOCKET_MOBILE_URL || 'ws://localhost:3025';
    // testProgress.value.logs.push('🔗 Connecting to WebSocket server...');

    // Generate unique client ID for this test run to prevent interference
    currentClientId = generateUniqueClientId();
    testProgress.value.logs.push(`🆔 Generated unique client ID: ${currentClientId}`);

    // Create a new WebSocket client for each test run with unique client ID
    wsClient = new TestWebSocketClient(fetchedApiKey, wsUrl, userJwtToken || undefined, currentClientId);

    // Set up event handlers
    wsClient.onConnected = () => {
      // testProgress.value.logs.push('✅ Connected to WebSocket server');
    };

    wsClient.onDisconnected = () => {
      console.log('⚠️ WebSocket disconnected, starting polling for test completion');
      // Start polling for test completion if test is still running
      if (testProgress.value.status === 'running' || testProgress.value.status === 'queued') {
        testProgress.value.logs.push('⚠️ Connection lost, monitoring test completion...');
        if (currentClientId) {
          startTestCompletionPolling(currentClientId);
        }
      }
    };

    wsClient.onTestStart = (data) => {
      console.log('Test started:', data);
      testProgress.value.status = 'running';
      testProgress.value.message = 'Test execution started';
      testProgress.value.logs.push('🚀 Test execution started');
    };

    wsClient.onTestOutput = (data) => {
      if (data.output) {
        // Clean and add the log entry
        addLogEntry(data.output);
        console.log('Test output:', data.output);
      }
    };

    wsClient.onTestComplete = (data) => {
      console.log('🎯 Frontend onTestComplete handler called with data:', data);

      // Stop polling since we received the completion message
      stopTestCompletionPolling();

      // Immediately update status to reflect final result
      // Use the actual test result status (passed/failed) directly
      const finalStatus = data.status || 'failed'; // Fallback to 'failed' if status is undefined
      console.log(`🎯 Setting testProgress.status to: ${finalStatus}`);
      testProgress.value.status = finalStatus;
      testProgress.value.message = finalStatus === 'passed' ? 'Test passed successfully' : 'Test failed';

      // Add completion message
      const completionMessage = finalStatus === 'passed' ? '✅ Test passed successfully' : '❌ Test failed';
      testProgress.value.logs.push(completionMessage);

      if (data.message) {
        testProgress.value.logs.push(data.message);
      }

      // Immediately stop running state
      console.log('🎯 Setting testRunning and isRunning to false');
      testRunning.value = false;
      isRunning.value = false;

      console.log(`🎯 Test final status: ${finalStatus}, testRunning: ${testRunning.value}, testProgress.status: ${testProgress.value.status}`);

      // Merge detailed execution logs with current logs
      setTimeout(async () => {
        await mergeDetailedLogs();
        console.log('🔄 Detailed logs merged after WebSocket completion');
      }, 1000);
    };

    wsClient.onTestError = (data) => {
      console.error('Test error:', data);

      // Stop polling since we received an error
      stopTestCompletionPolling();

      testProgress.value.status = 'failed';
      testProgress.value.message = 'Test execution failed';

      // Clean the error message
      const cleanedError = cleanLogForDisplay(data.message || 'Unknown error');
      addLogEntry(`❌ Error: ${cleanedError}`);

      // Explicitly set testRunning to false
      testRunning.value = false;
      isRunning.value = false;
    };

    // Add queue-related event handlers
    wsClient.onTestQueued = (data) => {
      console.log('Test queued:', data);
      testProgress.value.status = 'queued';
      testProgress.value.message = data.message || 'Test queued successfully';

      if (data.position && data.position > 1) {
        testProgress.value.logs.push(`📋 Test queued at position ${data.position}`);
        testProgress.value.logs.push(`⏳ ${data.queueStats?.active || 0} test(s) currently running`);
      } else {
        testProgress.value.logs.push('📋 Test queued and will start shortly');
      }

      // Start polling immediately when test is queued as a backup mechanism
      console.log('🔄 Starting backup polling for queued test');
      setTimeout(() => {
        if (currentClientId) {
          startTestCompletionPolling(currentClientId);
        }
      }, 2000); // Start polling after 2 seconds to give WebSocket a chance
    };

    wsClient.onQueueStatus = (data) => {
      console.log('Queue status update:', data);
      if (data.status === 'active') {
        testProgress.value.status = 'running';
        testProgress.value.message = 'Test is now running';
        testProgress.value.logs.push('🚀 Your test is now starting...');
      } else if (data.status === 'waiting') {
        testProgress.value.logs.push('⏳ Still waiting in queue...');
      } else if (data.status === 'completed') {
        // Handle completion via queue status
        console.log('🎯 Test completed via queue status');
        testRunning.value = false;
        isRunning.value = false;

        // Get the actual test result status from database after a delay
        setTimeout(async () => {
          try {
            const backendUrl = (import.meta as any).env.VITE_BACKEND_URL;
            const response = await axios.get(`${backendUrl}/temp-test-results/test-case/${selectedTestCase.value?.id}`);

            if (response.data && response.data.length > 0) {
              const latestResult = response.data[0];
              const actualStatus = latestResult.status;

              // Update status with actual result
              testProgress.value.status = actualStatus;
              testProgress.value.message = actualStatus === 'passed' ? 'Test passed successfully' : 'Test failed';

              const completionMessage = actualStatus === 'passed' ? '✅ Test passed successfully' : '❌ Test failed';
              testProgress.value.logs.push(completionMessage);

              console.log(`🎯 Queue completion: actual status is ${actualStatus}`);

              // Merge detailed logs
              await mergeDetailedLogs();
              console.log('🔄 Detailed logs merged after queue completion');
            }
          } catch (error) {
            console.error('Failed to get actual test result:', error);
            // Fallback to completed status
            testProgress.value.status = 'completed';
            testProgress.value.message = 'Test completed';
            testProgress.value.logs.push('✅ Test completed');
          }
        }, 2000); // Longer delay to ensure backend has processed the result
      } else if (data.status === 'failed') {
        // Handle failure via queue status
        console.log('🎯 Test failed via queue status');
        testProgress.value.status = 'failed';
        testProgress.value.message = 'Test failed';
        testProgress.value.logs.push('❌ Test failed');
        if (data.message) {
          testProgress.value.logs.push(`❌ Error: ${data.message}`);
        }
        testRunning.value = false;
        isRunning.value = false;

        // Merge detailed execution logs for failed tests
        setTimeout(async () => {
          await mergeDetailedLogs();
          console.log('🔄 Detailed logs merged after queue failure');
        }, 1000);
      }
    };

    wsClient.onError = (error) => {
      console.error('WebSocket error:', error);

      // Don't immediately fail if it's a connection timeout - the queue system should handle this
      if (error.includes('Connection timeout') || error.includes('Unable to establish connection')) {
        testProgress.value.status = 'queued';
        testProgress.value.message = 'Connecting to test server...';
        testProgress.value.logs.push(`⏳ ${error}`);
        testProgress.value.logs.push('🔄 Retrying connection... Your test will be queued when connection is established.');
      } else {
        testProgress.value.status = 'failed';
        testProgress.value.message = 'WebSocket error';
        testProgress.value.logs.push(`❌ Error: ${error}`);

        // Only set testRunning to false for actual failures, not connection issues
        testRunning.value = false;
        isRunning.value = false;
      }
    };

    // Wait a moment for WebSocket connection to establish
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Prepare test execution request - convert multiple actions to execution steps
    const executionSteps = convertToExecutionSteps(automationSteps.value);

    // Debug: Log the execution steps to see what actions are being sent
    console.log('🔍 Execution steps being sent to WebSocket:', executionSteps);

    // Check if there are any execution steps
    if (executionSteps.length === 0) {
      console.log('⚠️ No execution steps found. Cannot run test without actions.');
      alert('No automation actions defined. Please add actions to your test steps before running.');
      isRunning.value = false;
      return;
    }

    const testExecutionRequest: TestExecutionRequest = {
      testCaseId: selectedTestCase.value?.id ?? '',
      tcId: tcId,
      projectId: projectId, // Add the projectId from the route
      steps: executionSteps,
      testCase: {
        title: selectedTestCase.value?.title ?? '',
        precondition: selectedTestCase.value?.precondition,
        expectation: selectedTestCase.value?.expectation,
        projectId: projectId // Also include it in the testCase object
      }
    };

    // Execute the test
    wsClient.executeTest(testExecutionRequest);

  } catch (error) {
    console.error('Failed to start test execution:', error);
    testRunning.value = false;
    testProgress.value.status = 'failed';
    testProgress.value.error = error instanceof Error ? error.message : 'Unknown error';
    testProgress.value.logs.push(`❌ Failed to start test: ${testProgress.value.error}`);
  }
};

const clearLogs = () => {
  testProgress.value.logs = [];
  testProgress.value.error = '';
};

// Function to check queue status
const checkQueueStatus = async () => {
  try {
    // Use WebSocket server URL (port 3021) for queue stats, not the backend URL (port 3010)
    const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_MOBILE_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3025';
    const response = await axios.get(`${wsServerUrl}/api/queue/stats`);

    if (response.data && response.data.success) {
      queueStatus.value = {
        ...response.data.data,
        lastChecked: new Date()
      };
      return response.data.data;
    }
  } catch (error) {
    console.error('Failed to check queue status:', error);
  }
  return null;
};

// Function to check if a specific test case is already running
const checkTestCaseRunning = async (testCaseId: string) => {
  try {
    const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_MOBILE_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3025';
    const response = await axios.get(`${wsServerUrl}/api/queue/running-test-cases`);

    if (response.data && response.data.success) {
      const runningTestCases = response.data.data || [];
      return runningTestCases.includes(testCaseId);
    }
  } catch (error) {
    console.error('Failed to check running test cases:', error);
  }
  return false;
};

// Function to reload test results without overwriting current execution logs
const reloadTestResultsPreservingLogs = async () => {
  try {
    if (!selectedTestCase.value?.id) {
      return;
    }

    const backendUrl = (import.meta as any).env.VITE_BACKEND_URL;
    const response = await axios.get(`${backendUrl}/temp-test-results/test-case/${selectedTestCase.value.id}`);

    if (response.data && response.data.length > 0) {
      // Sort by execution date to get the most recent
      const sortedResults = response.data.sort((a: any, b: any) => {
        const aTime = new Date(a.executedAt || a.createdAt).getTime();
        const bTime = new Date(b.executedAt || b.createdAt).getTime();
        return bTime - aTime;
      });

      const latestResult = sortedResults[0];

      // Update test progress status and message without touching logs
      // Use the actual database status (passed/failed) instead of converting to completed
      testProgress.value.status = latestResult.status;
      if (latestResult.status === 'passed') {
        testProgress.value.message = 'Test passed successfully';
      } else if (latestResult.status === 'failed') {
        testProgress.value.message = 'Test failed';
      }

      console.log('🔄 Test status updated without overwriting execution logs');
    }
  } catch (error) {
    console.error('Failed to reload test results:', error);
  }
};

// Function to merge detailed execution logs with current logs
const mergeDetailedLogs = async () => {
  try {
    if (!selectedTestCase.value?.id) {
      console.log('No test case selected, skipping detailed logs merge');
      return;
    }

    const backendUrl = (import.meta as any).env.VITE_BACKEND_URL;
    const response = await axios.get(`${backendUrl}/temp-test-results/test-case/${selectedTestCase.value.id}`);

    if (response.data && response.data.length > 0) {
      // Sort by execution date to get the most recent
      const sortedResults = response.data.sort((a: any, b: any) => {
        const aTime = new Date(a.executedAt || a.createdAt).getTime();
        const bTime = new Date(b.executedAt || b.createdAt).getTime();
        return bTime - aTime;
      });

      const latestResult = sortedResults[0];

      // Try to load detailed logs
      if (latestResult.id) {
        try {
          const logsResponse = await axios.get(`${backendUrl}/temp-test-results/${latestResult.id}/logs`);

          if (logsResponse.data && logsResponse.data.logs && logsResponse.data.logs.length > 0) {
            // Store current execution logs (preserve the last 2 entries which are completion messages)
            const executionLogs = [...testProgress.value.logs];
            const lastTwoLogs = executionLogs.slice(-2);
            const initialLogs = executionLogs.slice(0, -2);

            // Clean and add detailed test execution logs
            const detailedLogs = logsResponse.data.logs.map(cleanLogForDisplay).filter(Boolean);

            // Merge: initial execution logs, detailed logs, then completion messages
            testProgress.value.logs = [
              ...initialLogs,
              ...detailedLogs,
              ...lastTwoLogs
            ];

            console.log(`🔄 Merged ${detailedLogs.length} detailed logs with execution logs`);
          }
        } catch (logsError) {
          console.error('Failed to load detailed logs:', logsError);
        }
      }
    }
  } catch (error) {
    console.error('Failed to merge detailed logs:', error);
  }
};

// Function to force stop a running test
const forceStopTest = () => {
  console.log('🛑 Force stopping test execution');
  testProgress.value.logs.push('🛑 Test execution manually stopped');
  testProgress.value.status = 'failed';
  testProgress.value.message = 'Test execution stopped by user';
  testRunning.value = false;
  isRunning.value = false;

  // Stop polling
  stopTestCompletionPolling();

  // Disconnect WebSocket if connected
  if (wsClient) {
    wsClient.disconnect();
    wsClient = null;
  }

  // Reload test results in case there were any completed before stopping
  setTimeout(async () => {
    await reloadTestResultsPreservingLogs();
    console.log('🔄 Test results reloaded after manual stop');
  }, 500);
};

// Polling mechanism to check test completion when WebSocket is disconnected
let testCompletionPoller: NodeJS.Timeout | null = null;
let pollingStartTime: number | null = null;
const POLLING_TIMEOUT = 15 * 60 * 1000; // 15 minutes timeout

const startTestCompletionPolling = (clientId: string) => {
  // Clear any existing poller
  if (testCompletionPoller) {
    clearInterval(testCompletionPoller);
  }

  console.log('🔄 Starting test completion polling for client:', clientId);
  testProgress.value.logs.push('🔄 Starting background monitoring for test completion...');

  pollingStartTime = Date.now();

  testCompletionPoller = setInterval(async () => {
    // Check for timeout
    if (pollingStartTime && Date.now() - pollingStartTime > POLLING_TIMEOUT) {
      console.log('⏰ Polling timeout reached, forcing completion');
      testProgress.value.logs.push('⏰ Test monitoring timeout reached - forcing completion');
      testProgress.value.status = 'failed';
      testProgress.value.message = 'Test execution timed out';
      testRunning.value = false;
      isRunning.value = false;
      stopTestCompletionPolling();
      return;
    }

    // Only poll if test is still running or queued
    if (testProgress.value.status === 'running' || testProgress.value.status === 'queued') {
      try {
        console.log('🔍 Polling for test completion...', {
          status: testProgress.value.status,
          clientId: clientId,
          currentClientId: currentClientId
        });

        // First, check BullMQ queue status to see if there are any active jobs
        const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_MOBILE_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3025';
        const queueResponse = await axios.get(`${wsServerUrl}/api/queue/stats`);

        if (queueResponse.data && queueResponse.data.success) {
          const queueStats = queueResponse.data.data;
          console.log('🔍 Queue stats:', queueStats);

          // If no active jobs and we're still in queued/running state, the test likely completed
          if (queueStats.active === 0 && (testProgress.value.status === 'running' || testProgress.value.status === 'queued')) {
            console.log('🔍 No active jobs but test still running/queued - test likely completed, checking for results');
          } else if (queueStats.active > 0) {
            console.log('🔍 Jobs still active in queue, continuing to wait...');
          }
        }

        // Check if there are any recent test results for this test case
        const backendUrl = (import.meta as any).env.VITE_BACKEND_URL;
        const testCaseId = selectedTestCase.value?.id;
        const apiUrl = `${backendUrl}/temp-test-results/test-case/${testCaseId}`;

        console.log('🔍 Polling API details:', {
          backendUrl,
          testCaseId,
          apiUrl,
          hasSelectedTestCase: !!selectedTestCase.value
        });

        const response = await axios.get(apiUrl);

        if (response.data && response.data.length > 0) {
          // Sort by execution date to get the most recent execution
          const sortedResults = response.data.sort((a: any, b: any) => {
            const aTime = new Date(a.executedAt || a.createdAt).getTime();
            const bTime = new Date(b.executedAt || b.createdAt).getTime();
            return bTime - aTime;
          });
          const latestResult = sortedResults[0];

          // Use executedAt if available, otherwise fall back to createdAt
          const resultTime = new Date(latestResult.executedAt || latestResult.createdAt);
          const testStartTime = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago

          console.log('🔍 Latest result:', {
            id: latestResult.id,
            status: latestResult.status,
            createdAt: latestResult.createdAt,
            executedAt: latestResult.executedAt,
            resultTime: resultTime.toISOString(),
            testStartTime: testStartTime.toISOString(),
            isRecent: resultTime > testStartTime
          });

          // Check if this result is for the current test session
          // Only complete if no active jobs AND we have a recent test result
          const hasNoActiveJobs = queueResponse.data?.success && queueResponse.data.data?.active === 0;
          const hasRecentResult = resultTime > testStartTime;
          const shouldComplete = hasNoActiveJobs && hasRecentResult;

          if (shouldComplete) {
            console.log('🎯 Found test result and no active jobs - completing test', {
              hasNoActiveJobs,
              hasRecentResult,
              resultTime: resultTime.toISOString(),
              shouldComplete
            });
            testProgress.value.logs.push('🎯 Found completed test execution via polling');

            // Update the frontend status based on the actual test result
            // Use the database status directly (passed/failed) instead of converting
            testProgress.value.status = latestResult.status;
            testProgress.value.message = latestResult.status === 'passed' ? 'Test passed successfully' : 'Test failed';

            const completionMessage = latestResult.status === 'passed' ? '✅ Test passed successfully' : '❌ Test failed';
            testProgress.value.logs.push(completionMessage);
            testProgress.value.logs.push('📊 Status updated via polling (WebSocket connection was lost)');

            // Add error details if test failed
            if (latestResult.status === 'failed' && latestResult.errorMessage) {
              const cleanedError = cleanLogForDisplay(latestResult.errorMessage);
              if (cleanedError) {
                testProgress.value.logs.push(`❌ Error: ${cleanedError}`);
              }
            }

            // Stop running state - the job execution is complete regardless of test outcome
            testRunning.value = false;
            isRunning.value = false;

            // Stop polling
            stopTestCompletionPolling();

            // Merge detailed execution logs with current logs
            setTimeout(async () => {
              await mergeDetailedLogs();
              console.log('🔄 Detailed logs merged after polling completion');
            }, 1000); // Small delay to ensure backend has processed the result
          } else {
            console.log('🔍 No recent test results found, continuing to poll...');
          }
        } else {
          console.log('🔍 No test results found, continuing to poll...');

          // If no active jobs in queue and no test results, something went wrong
          // Force completion to prevent infinite running state
          if (queueResponse.data?.success && queueResponse.data.data?.active === 0) {
            console.log('🚨 No active jobs and no test results - forcing completion to prevent stuck state');
            testProgress.value.logs.push('⚠️ Test execution completed but no results found');
            testProgress.value.status = 'failed';
            testProgress.value.message = 'Test completed but results not found';
            testRunning.value = false;
            isRunning.value = false;
            stopTestCompletionPolling();
          }
        }
      } catch (error) {
        console.error('Error polling for test completion:', error);

        // Add detailed error logging
        if (axios.isAxiosError(error)) {
          console.error('🔍 Axios error details:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url,
            message: error.message
          });
          testProgress.value.logs.push(`⚠️ API Error: ${error.response?.status || error.message}`);
        } else {
          console.error('🔍 Non-axios error:', error);
          testProgress.value.logs.push('⚠️ Error checking test status, retrying...');
        }
      }
    } else {
      // Test is no longer running, stop polling
      console.log('🛑 Test no longer running, stopping polling');
      stopTestCompletionPolling();
    }
  }, 3000); // Poll every 3 seconds (more frequent)
};

const stopTestCompletionPolling = () => {
  if (testCompletionPoller) {
    console.log('🛑 Stopping test completion polling');
    clearInterval(testCompletionPoller);
    testCompletionPoller = null;
    pollingStartTime = null;
  }
};

// Function to fetch AgentQ API key from backend
const fetchAgentQApiKey = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/api-keys`
    );

    if (response.data && response.data.length > 0) {
      // Find the AgentQ API key
      const agentqKey = response.data.find((key: any) => key.provider === 'agentq');
      if (agentqKey) {
        agentqApiKey.value = agentqKey.apiKey;
        console.log('AgentQ API key fetched successfully');
        return agentqKey.apiKey;
      } else {
        console.error('No AgentQ API key found');
        testProgress.value.logs.push('⚠️ No AgentQ API key found in backend');
        return null;
      }
    } else {
      console.error('No API keys found');
      testProgress.value.logs.push('⚠️ No API keys configured in backend');
      return null;
    }
  } catch (error) {
    console.error('Failed to fetch AgentQ API key:', error);
    testProgress.value.logs.push('❌ Failed to fetch AgentQ API key from backend');
    return null;
  }
};

// Mobile app selection functions
const openMobileAppModal = (action: any) => {
  currentActionForApp.value = action;
  showMobileAppModal.value = true;
};

const closeMobileAppModal = () => {
  showMobileAppModal.value = false;
  currentActionForApp.value = null;
};

const selectMobileApp = (app: any) => {
  if (currentActionForApp.value) {
    // The app object now has the correct mapped properties from the modal
    currentActionForApp.value.value = app.fileName; // This is now mapped from originalName
    currentActionForApp.value.fileUrl = app.fileUrl;
    currentActionForApp.value.fileId = app.id;

    console.log('Selected mobile app:', {
      fileName: app.fileName,
      fileUrl: app.fileUrl,
      fileId: app.id,
      platform: app.platform
    });
  }
  closeMobileAppModal();
};

// Device selection functions
const openDeviceModal = (action: any) => {
  currentActionForDevice.value = action;
  showDeviceModal.value = true;
};

const closeDeviceModal = () => {
  showDeviceModal.value = false;
  currentActionForDevice.value = null;
};

const selectDevice = (device: any) => {
  if (currentActionForDevice.value) {
    const deviceName = device.name || device.deviceName || 'Unknown Device';
    const deviceInfo = `${deviceName} (${device.platform || device.platformName || 'Unknown'})`;

    // Use deviceInfo property instead of value to avoid conflict with app file
    currentActionForDevice.value.deviceInfo = deviceInfo;
    currentActionForDevice.value.deviceId = device.id || device.udid || device.deviceId;
    currentActionForDevice.value.deviceName = deviceName;
    currentActionForDevice.value.platform = device.platform || device.platformName;
    currentActionForDevice.value.version = device.version || device.platformVersion;

    console.log('Selected device:', {
      deviceInfo,
      deviceId: currentActionForDevice.value.deviceId,
      deviceName,
      platform: currentActionForDevice.value.platform,
      version: currentActionForDevice.value.version
    });
  }
  closeDeviceModal();
};

const editTest = () => {
  console.log('🔧 editTest() called - entering edit mode');
  isEditing.value = true;

  // For each step, initialize UI state based on existing data
  automationSteps.value.forEach(step => {
    console.log(`🔧 Initializing step ${step.step} for editing:`, {
      hasActions: !!step.actions,
      actionsLength: step.actions?.length || 0,
      hasLegacyAction: !!step.action,
      legacyAction: step.action,
      hasFileUrl: !!step.fileUrl,
      hasFileId: !!step.fileId,
      hasValue: !!step.value,
      currentSelectedFile: step.selectedFile
    });
    // Convert single action to actions array if needed
    if (!step.actions && step.action) {
      step.actions = [{
        id: generateActionId(),
        action: step.action,
        target: step.target || '',
        value: step.value || ''
      }];
      // Keep the old fields for backward compatibility during editing
      step.target = step.target || '';
      step.value = step.value || '';
    } else if (!step.actions) {
      // Initialize empty actions array
      step.actions = [];
      step.target = step.target || '';
      step.value = step.value || '';
    }

    // Convert existing prompts to the "prompt" action type
    if (step.prompt && !step.action && step.actions.length === 0) {
      step.actions = [{
        id: generateActionId(),
        action: 'prompt',
        target: '',
        value: step.prompt
      }];
      step.prompt = '';
    }

    // Clean up any unsaved file metadata in actions
    if (step.actions) {
      step.actions.forEach((action: any) => {
        // Only keep file metadata for upload actions that have both fileUrl and fileId
        if (action.action === 'upload' && (!action.fileUrl || !action.fileId)) {
          console.log(`Clearing incomplete file data for action in step ${step.step}`);
          action.value = '';
          action.fileUrl = '';
          action.fileId = '';
        }
      });
    }

    // Reconstruct selectedFile object for uploaded files
    // Check both legacy single action format and new multiple actions format
    const hasUploadFileData = (step.action === 'upload' && step.fileUrl && step.fileId && step.value) ||
                             (step.actions && step.actions.some((action: any) =>
                               action.action === 'upload' && action.value && (action.fileUrl || step.fileUrl)
                             ));

    console.log(`🔍 File reconstruction check for step ${step.step}:`, {
      hasUploadFileData,
      legacyCheck: {
        hasAction: step.action === 'upload',
        hasFileUrl: !!step.fileUrl,
        hasFileId: !!step.fileId,
        hasValue: !!step.value
      },
      multipleActionsCheck: {
        hasActions: !!step.actions,
        uploadActions: step.actions?.filter((a: any) => a.action === 'upload').map((a: any) => ({
          hasValue: !!a.value,
          hasFileUrl: !!a.fileUrl,
          hasStepFileUrl: !!step.fileUrl,
          value: a.value
        }))
      }
    });

    if (hasUploadFileData) {
      // For legacy single action format
      if (step.action === 'upload' && step.fileUrl && step.fileId && step.value) {
        step.selectedFile = reconstructFileObject(step.value);
        console.log(`✅ Reconstructed selectedFile for legacy upload in step ${step.step}: ${step.value}`);
      }

      // For multiple actions format - find the upload action with file data
      if (step.actions) {
        step.actions.forEach((action: any, index: number) => {
          if (action.action === 'upload' && action.value && (action.fileUrl || step.fileUrl)) {
            // Use action-level file metadata if available, otherwise fall back to step-level
            const fileUrl = action.fileUrl || step.fileUrl;
            const fileId = action.fileId || step.fileId;

            console.log(`🔍 Upload action ${index} in step ${step.step}:`, {
              hasValue: !!action.value,
              value: action.value,
              hasActionFileUrl: !!action.fileUrl,
              hasStepFileUrl: !!step.fileUrl,
              finalFileUrl: fileUrl,
              finalFileId: fileId
            });

            if (fileUrl && fileId) {
              step.selectedFile = reconstructFileObject(action.value);
              console.log(`✅ Reconstructed selectedFile for upload action in step ${step.step}: ${action.value}`);
            } else {
              console.log(`❌ Missing fileUrl or fileId for upload action in step ${step.step}`);
            }
          }
        });
      }
    } else {
      console.log(`❌ No upload file data found for step ${step.step}`);
    }

    // Initialize dropdown states (all closed by default)
    step.showInteractionDropdown = false;
    step.showAssertionDropdown = false;

    // Final debug - check if selectedFile was set
    console.log(`🏁 Final state for step ${step.step}:`, {
      hasSelectedFile: !!step.selectedFile,
      selectedFileName: step.selectedFile?.name,
      hasFileUrl: !!step.fileUrl,
      hasFileId: !!step.fileId
    });
  });

  console.log('🔧 editTest() completed - edit mode initialized');
};

const saveTest = async () => {
  try {
    // Validate that at least one step has actions defined
    const hasAnyActions = automationSteps.value.some(step => {
      // Check if step has actions in the new format
      if (step.actions && step.actions.length > 0) {
        return step.actions.some((action: any) => action.action && action.action.trim() !== '');
      }
      // Check if step has action in the legacy format
      if (step.action && step.action.trim() !== '') {
        return true;
      }
      return false;
    });

    if (!hasAnyActions) {
      alert('Cannot save automation without any actions defined. Please add at least one action to your test steps.');
      return;
    }

    // Process steps to send clean payload structure
    const processedSteps = automationSteps.value.map(step => {
      const cleanStep: any = {
        step: step.step,
        stepName: step.stepName,
        showInteractionDropdown: false,
        showAssertionDropdown: false
      };

      if (step.actions && step.actions.length > 0) {
        // Clean actions array - remove IDs and only keep essential fields
        const cleanActions = step.actions.map((action: any) => {
          const cleanAction: any = {
            action: action.action,
            target: action.target || '',
            value: action.value || ''
          };

          // For upload actions, include file metadata if available
          if (action.action === 'upload') {
            // Use action-level file metadata if available, otherwise fall back to step-level
            const fileUrl = action.fileUrl || step.fileUrl;
            const fileId = action.fileId || step.fileId;

            if (fileUrl && fileId) {
              cleanAction.fileUrl = fileUrl;
              cleanAction.fileId = fileId;
            }

            // Set the filename as the value if not already set
            if (!cleanAction.value && step.selectedFile?.name) {
              cleanAction.value = step.selectedFile.name;
            }
          }

          // For setup actions, include mobile app and device metadata
          if (action.action === 'setup') {
            // Include mobile app metadata
            if (action.fileUrl) {
              cleanAction.fileUrl = action.fileUrl;
            }
            if (action.fileId) {
              cleanAction.fileId = action.fileId;
            }

            // Include device metadata
            if (action.deviceId) {
              cleanAction.deviceId = action.deviceId;
            }
            if (action.deviceName) {
              cleanAction.deviceName = action.deviceName;
            }
            if (action.platform) {
              cleanAction.platform = action.platform;
            }
            if (action.version) {
              cleanAction.version = action.version;
            }

            console.log('Setup action metadata:', {
              value: cleanAction.value,
              fileUrl: cleanAction.fileUrl,
              fileId: cleanAction.fileId,
              deviceId: cleanAction.deviceId,
              deviceName: cleanAction.deviceName,
              platform: cleanAction.platform,
              version: cleanAction.version
            });
          }

          return cleanAction;
        });

        // Store actions as JSON string in "Actions" field (capitalized as per DTO)
        cleanStep.Actions = JSON.stringify(cleanActions);
      } else if (step.action) {
        // Handle legacy single action format - convert to Actions array
        const singleAction = {
          action: step.action,
          target: step.target || '',
          value: step.value || ''
        };

        cleanStep.Actions = JSON.stringify([singleAction]);
      } else {
        // Step with no actions - empty actions array
        cleanStep.Actions = JSON.stringify([]);
      }

      return cleanStep;
    });

    // First update the test case details if they've changed
    if (selectedTestCase.value) {
      const updatedTestCase = {
        title: selectedTestCase.value.title,
        precondition: selectedTestCase.value.precondition,
        steps: selectedTestCase.value.steps,
        expectation: selectedTestCase.value.expectation
      };

      await axios.patch(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${selectedTestCase.value.id}`,
        updatedTestCase
      );
    }

    // Then save the automation steps using the processed steps
    console.log('Processed steps for backend:', processedSteps);

    const automationData = {
      testCaseId: selectedTestCase.value?.id,
      steps: processedSteps
    };
    
    await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation-mobile`,
      automationData
    );
    console.log('Test automation steps saved successfully');
    
    // edit test case type to automated
    await axios.patch(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/type`,
      { testCaseType: 'automation' }
    );
    console.log('Test case type updated to automated');

    // edit automated by agentq to true
    await axios.patch(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation-by-agentq`,
      { automationByAgentq: true }
    );
    console.log('Test case automation by AgentQ updated to true');

    isEditing.value = false;
  } catch (error) {
    console.error('Failed to save test:', error);
  }
};

const cancelEdit = () => {
  isEditing.value = false;
  // Reload the original test case to discard changes
  fetchTestCaseDetails();
};

const fetchAutomationSteps = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation-mobile`
    );

    if (response.data && response.data.steps && response.data.steps.length > 0) {
      // Use the saved automation steps and restore multiple actions if they exist
      automationSteps.value = response.data.steps.map((step: any) => {
        const processedStep = { ...step };

        // If step has Actions field (new format), parse it back to actions array
        if (step.Actions) {
          try {
            const parsedActions = JSON.parse(step.Actions);
            processedStep.actions = parsedActions.map((action: any) => {
              const restoredAction: any = {
                id: generateActionId(),
                action: action.action,
                target: action.target || '',
                value: action.value || ''
              };

              // For upload actions, restore file metadata to both action and step level
              if (action.action === 'upload' && action.fileUrl && action.fileId) {
                // Store file metadata in the action object for UI access
                restoredAction.fileUrl = action.fileUrl;
                restoredAction.fileId = action.fileId;

                // Also store at step level for backward compatibility
                processedStep.fileUrl = action.fileUrl;
                processedStep.fileId = action.fileId;

                // Create a mock selectedFile object for UI display
                if (action.value) {
                  processedStep.selectedFile = {
                    name: action.value,
                    type: 'application/octet-stream' // Default type
                  };
                }
                console.log(`Restored file metadata for upload action in step ${step.step}: ${action.fileUrl}`);
              }

              // For setup actions, restore mobile app and device metadata
              if (action.action === 'setup') {
                // Restore mobile app metadata
                if (action.fileUrl) {
                  restoredAction.fileUrl = action.fileUrl;
                }
                if (action.fileId) {
                  restoredAction.fileId = action.fileId;
                }

                // Restore device metadata
                if (action.deviceId) {
                  restoredAction.deviceId = action.deviceId;
                }
                if (action.deviceName) {
                  restoredAction.deviceName = action.deviceName;
                }
                if (action.platform) {
                  restoredAction.platform = action.platform;
                }
                if (action.version) {
                  restoredAction.version = action.version;
                }

                // Set deviceInfo for UI display
                if (action.deviceName && action.platform) {
                  restoredAction.deviceInfo = `${action.deviceName} (${action.platform})`;
                }

                console.log(`Restored setup metadata for step ${step.step}:`, {
                  app: action.value,
                  fileUrl: action.fileUrl,
                  device: restoredAction.deviceInfo,
                  deviceId: action.deviceId
                });
              }

              return restoredAction;
            });
            console.log(`Restored ${processedStep.actions.length} actions for step ${step.step}`);
          } catch (error) {
            console.error('Failed to parse Actions for step:', step.step, error);
            // Fallback to empty actions array if parsing fails
            processedStep.actions = [];
          }
        } else if (step.multipleActions) {
          // Handle old multipleActions format for backward compatibility
          try {
            processedStep.actions = JSON.parse(step.multipleActions);
            console.log(`Restored ${processedStep.actions.length} actions for step ${step.step} (legacy format)`);
          } catch (error) {
            console.error('Failed to parse multipleActions for step:', step.step, error);
            processedStep.actions = [];
          }
        } else if (step.action) {
          // Convert single action to actions array for consistency
          processedStep.actions = [{
            id: generateActionId(),
            action: step.action,
            target: step.target || '',
            value: step.value || ''
          }];
        } else {
          // No actions found
          processedStep.actions = [];
        }

        // Preserve file metadata for upload actions
        if (step.fileUrl && step.fileId) {
          processedStep.fileUrl = step.fileUrl;
          processedStep.fileId = step.fileId;
          console.log(`Restored file metadata for step ${step.step}: ${step.fileUrl}`);
        }

        console.log(`📥 Processed step ${step.step}:`, {
          stepName: processedStep.stepName,
          hasActions: !!processedStep.actions,
          actionsCount: processedStep.actions?.length || 0,
          hasFileUrl: !!processedStep.fileUrl,
          hasFileId: !!processedStep.fileId,
          actions: processedStep.actions?.map((a: any) => ({
            action: a.action,
            hasValue: !!a.value,
            hasFileUrl: !!a.fileUrl,
            hasFileId: !!a.fileId
          }))
        });

        return processedStep;
      });
      console.log('Loaded saved automation steps:', automationSteps.value);
    } else if (selectedTestCase.value) {
      // Generate steps from the test case if no saved automation exists
      automationSteps.value = parseStepsFromTestCase(selectedTestCase.value);
      console.log('Generated automation steps from test case:', automationSteps.value);
    }
  } catch (error) {
    console.error('Failed to fetch automation steps:', error);
    // Fall back to generating steps from the test case
    if (selectedTestCase.value) {
      automationSteps.value = parseStepsFromTestCase(selectedTestCase.value);
    }
  }
};

const fetchTestCaseDetails = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}`
    );
    
    if (response.data) {
      selectedTestCase.value = response.data;
      console.log('Found test case:', response.data);
      
      // Fetch automation steps after getting the test case
      await fetchAutomationSteps();
    } else {
      console.error('Test case not found with tcId:', tcId);
    }
  } catch (error) {
    console.error('Failed to fetch test case details:', error);
  }
};

// Load test case details and API key on mount
onMounted(async () => {
  await fetchTestCaseDetails();
  await fetchAgentQApiKey();
  // Load historical test results if available
  await loadHistoricalResults();
  // Check queue status initially
  await checkQueueStatus();

  // Set up periodic queue status checking (every 5 seconds)
  const queueCheckInterval = setInterval(async () => {
    await checkQueueStatus();
  }, 5000);

  // Store interval ID for cleanup
  (window as any).queueCheckInterval = queueCheckInterval;
});

// Cleanup WebSocket connection on unmount
onUnmounted(() => {
  if (wsClient) {
    wsClient.disconnect();
    wsClient = null;
  }

  // Clean up test completion polling
  stopTestCompletionPolling();

  // Clean up queue check interval
  if ((window as any).queueCheckInterval) {
    clearInterval((window as any).queueCheckInterval);
    (window as any).queueCheckInterval = null;
  }
});

// Add a new action to a step
const addActionToStep = (step: any) => {
  if (!step.actions) {
    step.actions = [];
  }

  step.actions.push({
    id: generateActionId(),
    action: '',
    target: '',
    value: ''
  });
};

// Remove an action from a step
const removeActionFromStep = (step: any, actionId: string) => {
  if (step.actions) {
    step.actions = step.actions.filter((action: any) => action.id !== actionId);
  }
};

// Set action for a specific action within a step
const setActionForStepAction = (stepAction: any, actionType: string) => {
  stepAction.action = actionType;

  // Set default values based on action
  if (actionType === 'goto') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'click') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'write') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'pause') {
    stepAction.target = '';
    stepAction.value = '1'; // Default to 1 second
  } else if (actionType === 'upload') {
    stepAction.target = 'input[type="file"]'; // Default file input selector
    stepAction.value = '';
  } else if (actionType === 'assertText') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'assertUrl') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'assertVisible') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'prompt') {
    stepAction.target = '';
    stepAction.value = '';
  }
};

// Handle file selection for individual actions in multiple actions
const handleActionFileSelect = async (step: any, action: any, event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) return;

  try {
    // Set uploading state
    action.uploading = true;

    // Upload file immediately to backend
    const formData = new FormData();
    formData.append('file', file);
    formData.append('stepId', step.step.toString());
    formData.append('testCaseId', selectedTestCase.value?.id || '');

    const response = await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${tcId}/automation-mobile/upload-file`,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' }
      }
    );

    // Store the uploaded file metadata in the action
    action.value = file.name;
    action.fileUrl = response.data.fileUrl;
    action.fileId = response.data.fileId;

    // Also store at step level for backward compatibility
    step.fileUrl = response.data.fileUrl;
    step.fileId = response.data.fileId;

    console.log('File uploaded successfully for action:', response.data);
  } catch (error) {
    console.error('Failed to upload file for action:', error);
    action.fileError = 'Failed to upload file. Please try again.';
  } finally {
    action.uploading = false;
  }
};

// Handle file removal for individual actions
const handleActionFileRemove = async (step: any, action: any) => {
  console.log('🗑️ handleActionFileRemove called', { step: step.step, action: action.action, fileId: action.fileId, stepFileId: step.fileId });

  if (!action.fileId && !step.fileId) {
    console.log('No file ID found to remove');
    alert('No file ID found to remove');
    return;
  }

  // Use action.fileId if available, otherwise fall back to step.fileId
  const fileIdToDelete = action.fileId || step.fileId;

  try {
    // Set removing state
    action.removing = true;

    console.log('Removing file with ID:', fileIdToDelete);

    // Call delete endpoint with the correct file ID
    await axios.delete(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${tcId}/automation-mobile/files/${fileIdToDelete}`
    );

    // Clear file metadata from action
    action.value = '';
    action.fileUrl = '';
    action.fileId = '';

    // Clear file metadata from step level
    step.fileUrl = '';
    step.fileId = '';
    step.selectedFile = null;

    // Clear any error messages
    action.fileError = '';
    step.fileError = '';

    console.log('File removed successfully for action');
  } catch (error) {
    action.fileError = 'Failed to remove file. Please try again.';
  } finally {
    action.removing = false;
  }
};

// Helper function to determine log level from log message
const getLogLevel = (log: string): string => {
  const logLower = log.toLowerCase();
  if (logLower.includes('error') || logLower.includes('failed') || logLower.includes('❌')) {
    return 'error';
  } else if (logLower.includes('warn') || logLower.includes('warning') || logLower.includes('⚠️')) {
    return 'warning';
  } else if (logLower.includes('success') || logLower.includes('✅') || logLower.includes('passed')) {
    return 'success';
  } else {
    return 'info';
  }
};

// Function to load historical test results
const loadHistoricalResults = async () => {
  try {
    if (!selectedTestCase.value?.id) {
      console.log('No test case ID available, skipping historical results load');
      return;
    }
    
    console.log(`Loading historical results for test case: ${selectedTestCase.value.id}`);
    
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/test-case/${selectedTestCase.value.id}`
    );

    if (response.data && response.data.length > 0) {
      // Load the most recent test result
      const latestResult = response.data[0];
      console.log('Found historical test result:', latestResult);
      
      // Update test status
      testProgress.value.status = latestResult.status || 'idle';
      
      // If there's a duration, display it
      if (latestResult.duration) {
        testProgress.value.message = `Test ${latestResult.status} in ${(latestResult.duration / 1000).toFixed(1)}s`;
      } else {
        testProgress.value.message = `Test ${latestResult.status}`;
      }
      
      // If there's an error message, display it
      if (latestResult.errorMessage) {
        testProgress.value.error = latestResult.errorMessage;
      }
      
      // Only load historical logs if we don't have current execution logs
      // This prevents overwriting detailed execution logs with summary logs
      const hasCurrentLogs = testProgress.value.logs.length > 0;

      if (!hasCurrentLogs) {
        // Load logs either from the result object or fetch them separately
        if (latestResult.logs && latestResult.logs.length > 0) {
          // Clean logs before displaying them
          testProgress.value.logs = latestResult.logs.map(cleanLogForDisplay).filter(Boolean);
          console.log(`Loaded ${testProgress.value.logs.length} logs from result object`);
        } else if (latestResult.id) {
          // Try to fetch logs separately
          try {
            const logsResponse = await axios.get(
              `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/${latestResult.id}/logs`
            );

            if (logsResponse.data && logsResponse.data.logs) {
              // Clean logs before displaying them
              testProgress.value.logs = logsResponse.data.logs.map(cleanLogForDisplay).filter(Boolean);
              console.log(`Loaded ${testProgress.value.logs.length} logs from separate endpoint`);
            }
          } catch (logsError) {
            console.error('Failed to load logs separately:', logsError);
          }
        }

        // Clear the logged messages set when loading historical results
        loggedMessages.clear();

        // Add all loaded logs to the logged messages set to prevent duplicates
        testProgress.value.logs.forEach(log => {
          loggedMessages.add(log);
        });
      } else {
        console.log('Preserving current execution logs instead of loading historical logs');
      }
    } else {
      console.log('No historical test results found');
    }
  } catch (error) {
    console.error('Failed to load historical results:', error);
  }
};

// State for video playback
const videoUrl = ref<string>('');
const loadingVideo = ref(false);

// Function to load video for the current test result
const loadVideo = async () => {
  if (activeTab.value !== 'video') return;
  
  try {
    loadingVideo.value = true;
    
    // Find the latest test result for this test case
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/test-case/${selectedTestCase.value?.id}`
    );
    
    if (response.data && response.data.length > 0) {
      const latestResult = response.data[0];
      
      if (latestResult.id) {
        // Get the signed URL for the video
        const videoResponse = await axios.get(
          `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/${latestResult.id}/video`
        );
        
        if (videoResponse.data && videoResponse.data.videoUrl) {
          videoUrl.value = videoResponse.data.videoUrl;
          console.log('Video URL loaded:', videoUrl.value);
        } else {
          videoUrl.value = '';
          console.log('No video available for this test result');
        }
      }
    } else {
      videoUrl.value = '';
    }
  } catch (error) {
    console.error('Failed to load video:', error);
    videoUrl.value = '';
  } finally {
    loadingVideo.value = false;
  }
};

// Watch for tab changes to load video when the Video tab is selected
watch(activeTab, (newTab) => {
  if (newTab === 'video') {
    loadVideo();
  } else if (newTab === 'history') {
    // loadTestHistory();
  }
});
</script>

<template>
  <div class="automation-container">
    <div class="automation-header">
      <h2>{{ selectedTestCase?.title }}</h2>
      <div class="action-buttons">
        <template v-if="!isEditing">
          <div class="api-key-status">
            <span v-if="agentqApiKey" class="status-indicator api-connected">
              <span class="icon">🔑</span> API Key Ready
            </span>
            <span v-else class="status-indicator api-disconnected">
              <span class="icon">🔑</span> No API Key
            </span>
          </div>
          <div class="queue-status" v-if="queueStatus.active > 0 || queueStatus.waiting > 0">
            <span class="status-indicator queue-busy">
              <span class="icon">📊</span> Queue: {{ queueStatus.active }} active, {{ queueStatus.waiting }} waiting
            </span>
          </div>
          <div class="status-header" v-if="!(queueStatus.active >= 10)">
            <span v-if="testProgress.status === 'queued'" class="status-indicator queued">
              <span class="icon">📋</span> Test Queued
            </span>
            <span v-else-if="testRunning" class="status-indicator running">
              <span class="icon">🟡</span> Running Test...
            </span>
            <span v-else-if="testProgress.status === 'passed'" class="status-indicator passed">
              <span class="icon">✅</span> Test Passed
            </span>
            <span v-else-if="testProgress.status === 'failed'" class="status-indicator failed">
              <span class="icon">❌</span> Test Failed
            </span>
            <span v-else class="status-indicator idle">
              <span class="icon">⚪</span> Ready
            </span>
          </div>
          <button
            class="run-button"
            :class="{ 'run-button-disabled': !hasValidActions }"
            @click="runTest"
            :disabled="testRunning || testProgress.status === 'queued' || queueStatus.active >= 10 || !hasValidActions"
            :title="!hasValidActions ? 'Add at least one action before running test' : (queueStatus.active >= 10 ? 'Queue is full. Please wait.' : 'Run automation test')"
            v-if="!(queueStatus.active >= 10) && !testRunning && testProgress.status !== 'queued'"
          >
            <span class="icon">▶️</span>
            <span class="warning-text">Run Test</span>
            <span v-if="!hasValidActions" class="warning-text">(No actions)</span>
          </button>
          <button
            class="stop-button"
            @click="forceStopTest"
            v-if="testRunning || testProgress.status === 'queued'"
          >
            <span class="icon">⏹️</span>
            <span>{{ testProgress.status === 'queued' ? 'Cancel' : 'Stop Test' }}</span>
          </button>
          <button class="edit-button" @click="editTest" :disabled="testRunning">
            <span class="icon">✏️</span> Edit
          </button>
        </template>
        <template v-else>
          <button
            class="save-button"
            :disabled="!hasValidActions"
            :class="{ 'save-button-disabled': !hasValidActions }"
            @click="saveTest"
            :title="hasValidActions ? 'Save automation' : 'Add at least one action before saving'"
          >
            <span class="icon">💾</span> Save
            <span v-if="!hasValidActions" class="warning-text">(No actions defined)</span>
          </button>
          <button class="cancel-button" @click="cancelEdit">
            <span class="icon">❌</span> Cancel
          </button>
        </template>
      </div>
    </div>

    <div class="automation-content" :class="{ 'editing': isEditing }">
      <div class="steps-panel">
        <div v-for="step in automationSteps" :key="step.step" class="step-item">
          <div class="step-header">Step {{ step.step }}</div>
          <div class="step-details">
            <div v-if="step.stepName" class="step-name">{{ step.stepName }}</div>
            
            <!-- View mode - show action details -->
            <div v-if="!isEditing" class="step-action-details">
              <!-- Show multiple actions if they exist -->
              <div v-if="step.actions && step.actions.length > 0" class="multiple-actions">
                <div v-for="(action, index) in step.actions" :key="action.id" class="step-action">
                  <div class="action-number" v-if="step.actions.length > 1">Action {{ index + 1 }}:</div>
                  <div v-if="action.action === 'setup'" class="action-content">
                    <strong>Setup</strong>
                    <div>App: {{ action.value }}</div>
                  </div>
                  <div v-if="action.action === 'prompt'" class="action-content">
                    <strong>Prompt</strong>
                    <div>Value: {{ action.value }}</div>
                  </div>
                  <div v-else-if="action.action === 'goto'" class="action-content">
                    <strong>Go to Page</strong>
                    <div>URL: {{ action.target }}</div>
                  </div>
                  <div v-else-if="action.action === 'click'" class="action-content">
                    <strong>Click</strong>
                    <div>Target: {{ action.target }}</div>
                  </div>
                  <div v-else-if="action.action === 'write'" class="action-content">
                    <strong>Fill</strong>
                    <div>Target: {{ action.target }}</div>
                    <div>Value: {{ action.value }}</div>
                  </div>
                  <div v-else-if="action.action === 'pause'" class="action-content">
                    <strong>Pause</strong>
                    <div>Duration: {{ action.value }} seconds</div>
                  </div>
                  <div v-else-if="action.action === 'upload'" class="action-content">
                    <strong>Upload File</strong>
                    <div>Target: {{ action.target }}</div>
                    <div>File: {{ action.value }}</div>
                  </div>
                  <div v-else-if="action.action === 'assertText'" class="action-content">
                    <strong>Assert Text</strong>
                    <div>Target: {{ action.target }}</div>
                    <div>Expected Text: {{ action.value }}</div>
                  </div>
                  <div v-else-if="action.action === 'assertUrl'" class="action-content">
                    <strong>Assert URL</strong>
                    <div>Expected URL: {{ action.value }}</div>
                  </div>
                  <div v-else-if="action.action === 'assertVisible'" class="action-content">
                    <strong>Assert Visible</strong>
                    <div>Target: {{ action.target }}</div>
                  </div>
                </div>
              </div>
              <!-- Fallback to single action display for backward compatibility -->
              <div v-else-if="step.action" class="single-action">
                <div v-if="step.action === 'prompt'" class="step-action">
                  <strong>Action: Prompt</strong>
                  <div>Value: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'goto'" class="step-action">
                  <strong>Action: Go to Page</strong>
                  <div>URL: {{ step.target }}</div>
                </div>
                <div v-else-if="step.action === 'click'" class="step-action">
                  <strong>Action: Click</strong>
                  <div>Target: {{ step.target }}</div>
                </div>
                <div v-else-if="step.action === 'write'" class="step-action">
                  <strong>Action: Fill</strong>
                  <div>Target: {{ step.target }}</div>
                  <div>Value: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'pause'" class="step-action">
                  <strong>Action: Pause</strong>
                  <div>Duration: {{ step.value }} seconds</div>
                </div>
                <div v-else-if="step.action === 'upload'" class="step-action">
                  <strong>Action: Upload File</strong>
                  <div>Target: {{ step.target }}</div>
                  <div>File: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'assertText'" class="step-action">
                  <strong>Action: Assert Text</strong>
                  <div>Target: {{ step.target }}</div>
                  <div>Expected Text: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'assertUrl'" class="step-action">
                  <strong>Action: Assert URL</strong>
                  <div>Expected URL: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'assertVisible'" class="step-action">
                  <strong>Action: Assert Visible</strong>
                  <div>Target: {{ step.target }}</div>
                </div>
              </div>
            </div>

            <!-- View mode - show message if no action is set up -->
            <div v-else-if="!isEditing" class="action-message">
              <div class="no-action-message">
                The action is not set up yet
              </div>
            </div>
            
            <!-- Edit mode - show action buttons and fields -->
            <div v-else class="step-actions-container">
              <!-- Add Action Button -->
              <div class="add-action-section">
                <button class="add-action-button" @click="addActionToStep(step)">
                  <span class="icon">➕</span> Add Action
                </button>
              </div>

              <!-- Display existing actions -->
              <div v-if="step.actions && step.actions.length > 0" class="actions-list">
                <div v-for="(action, index) in step.actions" :key="action.id" class="action-item">
                  <div class="action-header">
                    <span class="action-number">Action {{ index + 1 }}</span>
                    <button class="remove-action-button" @click="removeActionFromStep(step, action.id)">
                      <span class="icon">X</span>
                    </button>
                  </div>

                  <div class="action-buttons">
                    <div class="dropdown">
                      <button class="action-button setup-button" @click="setActionForStepAction(action, 'setup')">
                        Setup
                      </button>
                    </div>

                    <div class="dropdown">
                      <button class="action-button prompt-button" @click="setActionForStepAction(action, 'prompt')">
                        Prompt
                      </button>
                    </div>

                    <div class="dropdown">
                      <button class="action-button interaction-button" @click="action.showInteractionDropdown = !action.showInteractionDropdown">
                        Interaction
                      </button>
                      <div v-if="action.showInteractionDropdown" class="dropdown-content">
                        <div class="dropdown-item" @click="setActionForStepAction(action, 'goto'); action.showInteractionDropdown = false">Go to Page</div>
                        <div class="dropdown-item" @click="setActionForStepAction(action, 'click'); action.showInteractionDropdown = false">Click</div>
                        <div class="dropdown-item" @click="setActionForStepAction(action, 'write'); action.showInteractionDropdown = false">Fill</div>
                        <div class="dropdown-item" @click="setActionForStepAction(action, 'pause'); action.showInteractionDropdown = false">Pause</div>
                        <div class="dropdown-item" @click="setActionForStepAction(action, 'upload'); action.showInteractionDropdown = false">Upload File</div>
                      </div>
                    </div>

                    <div class="dropdown">
                      <button class="action-button assertion-button" @click="action.showAssertionDropdown = !action.showAssertionDropdown">
                        Assertion
                      </button>
                      <div v-if="action.showAssertionDropdown" class="dropdown-content">
                        <div class="dropdown-item" @click="setActionForStepAction(action, 'assertText'); action.showAssertionDropdown = false">Assert Text</div>
                        <div class="dropdown-item" @click="setActionForStepAction(action, 'assertUrl'); action.showAssertionDropdown = false">Assert URL</div>
                        <div class="dropdown-item" @click="setActionForStepAction(action, 'assertVisible'); action.showAssertionDropdown = false">Assert Visible</div>
                      </div>
                    </div>
                  </div>

                  <!-- Action fields for each action -->
                  <div v-if="action.action === 'setup'" class="action-fields">
                    <div class="field-header">
                      <label>App File:</label>
                    </div>
                      <div class="app-file-selection">
                        <input
                          v-model="action.value"
                          type="text"
                          placeholder="No app selected"
                          readonly
                          class="app-file-input"
                        />
                        <button
                          class="select-app-button"
                          @click="openMobileAppModal(action)"
                          type="button"
                        >
                          📱 Select App
                        </button>
                      </div>

                      <div class="field-header">
                      <label>Device:</label>
                    </div>
                      <div class="app-file-selection">
                        <input
                          v-model="action.deviceInfo"
                          type="text"
                          placeholder="No device selected"
                          readonly
                          class="app-file-input"
                        />
                        <button
                          class="select-device-button"
                          @click="openDeviceModal(action)"
                          type="button"
                        >
                          📱 Select Device
                        </button>
                      </div>
                  </div>

                  <div v-if="action.action === 'prompt'" class="action-fields">
                    <div class="field-header">
                      <label>Prompt:</label>
                    </div>
                    <input v-model="action.value" type="text" placeholder="Enter prompt for AI" />
                  </div>

                  <div v-if="action.action === 'goto'" class="action-fields">
                    <div class="field-header">
                      <label>URL:</label>
                    </div>
                    <input v-model="action.target" type="text" placeholder="Enter URL" />
                  </div>

                  <div v-if="action.action === 'click'" class="action-fields">
                    <div class="field-header">
                      <label>Target:</label>
                    </div>
                    <input v-model="action.target" type="text" placeholder="Enter selector" />
                  </div>

                  <div v-if="action.action === 'write'" class="action-fields">
                    <div class="field-header">
                      <label>Target:</label>
                    </div>
                    <input v-model="action.target" type="text" placeholder="Enter selector" />
                    <div class="field">
                      <label>Value:</label>
                      <input v-model="action.value" type="text" placeholder="Enter value" />
                    </div>
                  </div>

                  <div v-if="action.action === 'pause'" class="action-fields">
                    <div class="field-header">
                      <label>Duration (seconds):</label>
                    </div>
                    <input v-model="action.value" type="number" placeholder="Enter pause duration in seconds" />
                  </div>

                  <div v-if="action.action === 'upload'" class="action-fields">
                    <div class="field-header">
                      <label>Target:</label>
                    </div>
                    <input v-model="action.target" type="text" placeholder="Enter file input selector" />
                    <div class="field">
                      <label>File:</label>
                      <!-- Simple File Upload for Multiple Actions -->
                      <div class="simple-file-upload">
                        <!-- Show file input only when no file is uploaded -->
                        <input
                          v-if="!action.value || (!action.fileUrl && !step.fileUrl)"
                          type="file"
                          @change="handleActionFileSelect(step, action, $event)"
                          class="file-input-simple"
                          accept="image/*,application/pdf,.doc,.docx,.txt,.mp4,.avi,.mov,.csv"
                        />

                        <!-- Upload status -->
                        <div v-if="action.uploading" class="upload-status">
                          <span>Uploading...</span>
                        </div>
                        <div v-else-if="action.removing" class="upload-status">
                          <span>Removing...</span>
                        </div>

                        <!-- Show uploaded file info when file exists -->
                        <div v-else-if="action.value && (action.fileUrl || step.fileUrl)" class="selected-file-info">
                          <span class="file-name">📎 {{ action.value }}</span>
                          <span v-if="action.fileUrl || step.fileUrl" class="file-status">✅ Uploaded</span>
                          <button
                            class="remove-file-button"
                            @click.stop="handleActionFileRemove(step, action)"
                            title="Remove file"
                            :disabled="action.removing"
                          >
                            {{ action.removing ? '⏳' : '✕' }}
                          </button>
                        </div>

                        <!-- Show replace file option when file exists -->
                        <div v-if="action.value && (action.fileUrl || step.fileUrl) && !action.uploading && !action.removing" class="replace-file-option">
                          <label class="replace-file-label">
                            <input
                              type="file"
                              @change="handleActionFileSelect(step, action, $event)"
                              class="file-input-hidden"
                              accept="image/*,application/pdf,.doc,.docx,.txt,.mp4,.avi,.mov,.csv"
                              style="display: none;"
                            />
                          </label>
                        </div>
                        <div v-if="action.fileError" class="error-message">
                          {{ action.fileError }}
                        </div>
                      </div>
                      <!-- Legacy File Upload Component (hidden for multiple actions) -->
                      <div class="file-upload-container" style="display: none;">
                        <div 
                          class="file-upload-area"
                          :class="{ 
                            'dragover': step.isDragOver, 
                            'has-file': step.selectedFile,
                            'uploading': step.uploading  
                          }"
                          @dragover.prevent="handleDragOver(step)"
                          @dragenter.prevent="handleDragEnter(step)"
                          @dragleave.prevent="handleDragLeave(step, $event)"
                          @drop.prevent="handleDrop(step, $event)"
                          @click="triggerFileInput(step)"
                        >

                          <input
                            type="file"
                            class="file-input"
                            :ref="'fileInput' + step.step"
                            data-file-input="step.step"
                            @change="handleFileSelect(step, $event)"
                            accept="image/*,application/pdf,.doc,.docx,.txt,.mp4,.avi,.mov,.csv"
                          >
                          
                          <!-- Show upload progress -->
                          <div v-if="step.uploading" class="upload-progress">
                            <div class="spinner"></div>
                            <span>Uploading {{ step.selectedFile?.name || 'file' }}...</span>
                          </div>

                          <!-- Show empty state when no file and not uploading -->
                          <div v-else-if="!step.selectedFile" class="upload-placeholder">
                            <div class="upload-icon">📁</div>
                            <div class="file-upload-text">
                              Drag and drop files here or click to select file
                            </div>
                            <div class="file-upload-subtext">
                              Supports: Images, Documents, Videos
                            </div>
                          </div>

                          <!-- Show file info when file is selected and not uploading -->
                          <div v-else class="file-info">
                            <span class="file-icon">{{ getFileIcon(step.selectedFile.type) }}</span>
                            <span class="file-name">{{ step.selectedFile.name }}</span>
                            <div class="file-size">{{ formatFileSize(step.selectedFile.size) }}</div>
                          </div>
                        </div>
                        
                        <!-- Selected File Display -->
                        <div v-if="step.selectedFile" class="selected-file">
                          <div class="file-info">
                            <span class="file-icon">{{ getFileIcon(step.selectedFile.type) }}</span>
                            <div>
                              <div class="file-name">{{ step.selectedFile.name }}</div>
                              <div class="file-size">{{ formatFileSize(step.selectedFile.size) }}</div>
                            </div>
                          </div>
                          <button
                            class="remove-file"
                            @click="removeFile(step)"
                            :disabled="step.deleting"
                            :title="step.deleting ? 'Deleting file...' : 'Remove file'"
                          >
                            <span v-if="step.deleting" class="spinner-small"></span>
                            <span v-else>×</span>
                          </button>
                        </div>
                        
                        <!-- Error Message -->
                        <div v-if="step.fileError" class="error-message">
                          {{ step.fileError }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-if="action.action === 'assertText' || action.action === 'assertVisible'" class="action-fields">
                    <div class="field-header">
                      <label>Target:</label>
                    </div>
                    <input v-model="action.target" type="text" placeholder="Enter selector" />
                    <div class="field">
                      <label>Expected Text:</label>
                      <input v-model="action.value" type="text" placeholder="Enter expected text" />
                    </div>
                  </div>

                  <div v-if="action.action === 'assertUrl'" class="action-fields">
                    <div class="field-header">
                      <label>Expected URL:</label>
                    </div>
                    <input v-model="action.value" type="text" placeholder="Enter expected URL" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="preview-panel" :class="{ 'isEditing': isEditing }">
        <div class="tabs">
          <div 
            class="tab" 
            :class="{ active: activeTab === 'logs' }"
            @click="setActiveTab('logs')"
          >
            <span class="tab-icon">📋</span> Logs
          </div>
          <div 
            class="tab" 
            :class="{ active: activeTab === 'video' }"
            @click="setActiveTab('video')"
          >
            <span class="tab-icon">🎬</span> Video
          </div>
        </div>
        <div class="tab-content-container">
          <!-- Logs tab content -->
          <div v-if="activeTab === 'logs'" class="tab-content">
            <div class="logs-container">
              <!-- Test execution status -->
              <div v-if="testProgress.status !== 'idle'" class="status-container">
                <div class="status-header">
                  <span class="status-badge" :class="testProgress.status">
                    {{ testProgress.status.toUpperCase() }}
                  </span>
                  <span v-if="testProgress.totalSteps > 0" class="progress-info">
                    Step {{ testProgress.currentStep }} of {{ testProgress.totalSteps }}
                  </span>
                </div>
                <div v-if="testProgress.message" class="status-message">
                  {{ testProgress.message }}
                </div>
                <div v-if="testProgress.error" class="error-message">
                  <pre>{{ testProgress.error }}</pre>
                </div>
              </div>

              

              <!-- Real-time logs from test execution -->
              <div v-for="(log, index) in testProgress.logs" :key="index" class="log-entry">
                <span class="log-time">{{ new Date().toLocaleTimeString() }}</span>
                <span class="log-level" :class="getLogLevel(log)">{{ getLogLevel(log).toUpperCase() }}</span>
                <span class="log-message">{{ log }}</span>
              </div>

              <!-- Placeholder when no logs -->
              <div v-if="testProgress.logs.length === 0 && testProgress.status === 'idle'" class="no-logs">
                <p v-if="hasValidActions">No test execution logs yet. Click "Run Test" to start automation testing.</p>
                <p v-else>No automation actions defined. Please add actions to your test steps first, then click "Run Test".</p>
              </div>

              <!-- Clear logs button -->
              <div v-if="testProgress.logs.length > 0" class="logs-actions">
                <button class="clear-logs-button" @click="clearLogs()">
                  Clear Logs
                </button>
              </div>
            </div>
          </div>
          
          <!-- Video tab content -->
          <div v-if="activeTab === 'video'" class="tab-content">
            <div v-if="loadingVideo" class="loading-indicator">
              <div class="spinner"></div>
              <p>Loading video...</p>
            </div>
            
            <div v-else-if="!videoUrl" class="no-video">
              <p>No video recording available for this test run.</p>
              <p class="no-video-info">Videos are automatically recorded.</p>
            </div>
            
            <div v-else class="video-player">
              <video controls autoplay>
                <source :src="videoUrl" type="video/webm">
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile App Selection Modal -->
  <MobileAppSelectionModal
    :show="showMobileAppModal"
    :project-id="projectId"
    :tc-id="tcId"
    @close="closeMobileAppModal"
    @select="selectMobileApp"
  />

  <!-- Device Selection Modal -->
  <DeviceSelectionModal
    :show="showDeviceModal"
    @close="closeDeviceModal"
    @select="selectDevice"
  />
</template>

<style scoped>
.automation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.automation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e5e7eb;
}

.back-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 15px;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.test-case-title {
  font-weight: bold;
  margin-left: 20px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.api-key-status {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.status-indicator {
  font-size: 14px;
  padding: 8px 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.status-indicator.api-connected {
  background-color: #ecfdf5;
  color: #047857;
  border: 1px solid #d1fae5;
}

.status-indicator.api-disconnected {
  background-color: #fef2f2;
  color: #b91c1c;
  border: 1px solid #fee2e2;
}

.queue-status {
  margin-left: 8px;
}

.status-indicator.queue-busy {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-indicator.queued {
  background-color: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.status-indicator.running {
  background-color: #fffbeb;
  color: #92400e;
  border: 1px solid #fef3c7;
}

.status-indicator.passed {
  background-color: #ecfdf5;
  color: #047857;
  border: 1px solid #d1fae5;
}

.status-indicator.failed {
  background-color: #fef2f2;
  color: #b91c1c;
  border: 1px solid #fee2e2;
}

.status-indicator.idle {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.run-button {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stop-button {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.edit-button {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  color: #4b5563;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.save-button {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.save-button-disabled {
  background-color: #9ca3af !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.save-button-disabled:hover {
  background-color: #9ca3af !important;
  transform: none !important;
  box-shadow: none !important;
}

.warning-text {
  font-size: 14px;
  font-weight: 400;
  opacity: 0.9;
  color: #4b5563;
}

.cancel-button {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  color: #4b5563;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.edit-button:hover, .cancel-button:hover {
  background-color: #e5e7eb;
  color: #374151;
}

.run-button:hover:not(:disabled), .save-button:hover:not(:disabled) {
  background-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stop-button:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.run-button:disabled, .edit-button:disabled {
  background-color: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}

.run-button-disabled {
  background-color: #9ca3af !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.run-button-disabled:hover {
  background-color: #9ca3af !important;
  transform: none !important;
  box-shadow: none !important;
}



/* Main container layout */
.automation-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 180px);
  min-height: 500px;
  position: relative;
}

/* Steps panel styling with fixed width ratio */
.steps-panel {
  flex: 0 0 50%;
  width: 50%;
  max-width: 50%;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Preview panel styling with fixed width ratio */
.preview-panel {
  flex: 0 0 50%;
  width: 50%;
  max-width: 50%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: 100%;
}

/* Logs container with fixed height and scrolling */
.logs-container {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: calc(100vh - 280px);
  font-family: monospace;
  font-size: 13px;
  line-height: 1.5;
  background-color: #f8f9fa;
  border-radius: 4px;
}

/* Log entries with word wrapping to prevent horizontal overflow */
.log-entry {
  margin-bottom: 8px;
  padding: 6px 8px;
  background-color: white;
  border-radius: 4px;
  border-left: 3px solid #e5e7eb;
  word-break: break-word;
  white-space: pre-wrap;
}

/* Tab content container with fixed height */
.tab-content-container {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-top: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 45px); /* Subtract tab height */
}

.tab-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Video container with fixed dimensions */
.video-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

/* Make sure video maintains aspect ratio without causing layout shifts */
.video-player video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Ensure consistent styling in edit mode */
.automation-content.editing .steps-panel,
.automation-content.editing .preview-panel {
  height: 100%;
}

.step-item {
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: visible; /* Changed from hidden to visible */
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-header {
  font-weight: 600;
  padding: 12px 16px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-details {
  padding: 16px;
  width: 100%;
  overflow: visible;
}

.step-name {
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 16px;
  line-height: 1.5;
  padding: 8px 12px;
  background-color: #f9fafb;
  border-radius: 6px;
  border-left: 3px solid #10b981;
  word-break: break-word;
}

.step-action-details {
  margin-top: 12px;
  width: 100%;
}

.step-action {
  background-color: #f3f4f6;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
  width: 100%;
}

.step-action strong {
  display: block;
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.step-action div {
  margin-bottom: 6px;
  font-size: 13px;
  color: #4b5563;
  word-break: break-word;
}

.step-actions-container {
  margin-top: 16px;
  width: 100%;
}

.step-action-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  width: 100%;
}

/* Multiple actions styles */
.add-action-section {
  margin-bottom: 16px;
}

.add-action-button {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.add-action-button:hover {
  background-color: #059669;
  transform: translateY(-1px);
}

.actions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9fafb;
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.action-number {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.remove-action-button {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-action-button:hover {
  background-color: #dc2626;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.multiple-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-content {
  margin-left: 16px;
}

/* Simple file upload for multiple actions */
.simple-file-upload {
  margin-top: 8px;
}

.file-input-simple {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.selected-file-info {
  margin-top: 8px;
  padding: 8px;
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-weight: 500;
  color: #0f172a;
  flex: 1;
}

.file-status {
  color: #059669;
  font-size: 12px;
}

.remove-file-button {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.remove-file-button:hover {
  background-color: #dc2626;
}

.remove-file-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  margin-top: 8px;
  padding: 8px;
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: 4px;
  color: #dc2626;
  font-size: 14px;
}

.upload-status {
  margin-top: 8px;
  padding: 8px;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 4px;
  color: #92400e;
  font-size: 14px;
}

/* Replace file option styling */
.replace-file-option {
  margin-top: 8px;
}

.replace-file-label {
  cursor: pointer;
  display: inline-block;
}

.file-input-hidden {
  display: none !important;
}

.action-button {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  background-color: white;
  color: #4b5563;
}

.action-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.prompt-button {
  background-color: #eff6ff;
  color: #3b82f6;
  border-color: #dbeafe;
}

.prompt-button:hover {
  background-color: #dbeafe;
  color: #2563eb;
}

.interaction-button {
  background-color: #f3dbfe;
  color: #8f3bf6;
  border-color: #dbeafe;
}

.interaction-button:hover {
  background-color: #ebcbfa;
  color: #7520f4;
}

.assertion-button {
  background-color: #fef8db;
  color: #d0a01c;
  border-color: #dbeafe;
}

.assertion-button:hover {
  background-color: #fae57e;
  color: #9b7409;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 100; /* Higher z-index to ensure visibility */
  min-width: 180px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-top: 4px;
}

.dropdown-item {
  padding: 10px 16px;
  font-size: 13px;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.1s ease;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.dropdown-item:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.dropdown-item:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.action-fields {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 6px;
  margin-top: 12px;
  border: 1px solid #e5e7eb;
  width: 100%;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  width: 100%;
}

.field-header label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.close-field-button {
  background: none;
  border: none;
  font-size: 18px;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-field-button:hover {
  color: #ef4444;
}

.action-fields input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.action-fields input:focus {
  outline: none;
  border-color: #4caeeb;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.field {
  margin-bottom: 12px;
  width: 100%;
}

.field label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: #4b5563;
}

.field:last-child {
  margin-bottom: 0;
}

.no-action-message {
  padding: 12px;
  background-color: #f3f4f6;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  text-align: center;
  font-style: italic;
  border: 1px dashed #d1d5db;
}

.prompt-field {
  margin-top: 12px;
}

.prompt-field textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  min-height: 100px;
  resize: vertical;
  transition: all 0.2s ease;
}

.prompt-field textarea:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.prompt-field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.prompt-field-header label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.queued {
  background-color: #cce7ff;
  color: #0056b3;
}

.status-badge.running {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.progress-info {
  font-size: 14px;
  color: #6c757d;
}

.status-message {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
}

.error-message {
  font-size: 14px;
  color: #721c24;
  background-color: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 11px;
}

.log-level.info {
  color: #17a2b8;
}

.log-level.success {
  color: #28a745;
}

.log-level.warning {
  color: #ffc107;
}

.log-level.error {
  color: #dc3545;
}

.log-message {
  flex: 1;
  color: #495057;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.logs-actions {
  margin-top: 16px;
  text-align: right;
}

.clear-logs-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-button:hover {
  background-color: #5a6268;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
}

.no-video {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #9ca3af;
  padding: 20px;
}

.no-video p {
  margin: 8px 0;
}

.no-video-info {
  font-size: 14px;
  color: #6b7280;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #1e1e1e;
  color: #e5e7eb;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner-small {
  border: 2px solid rgba(255, 255, 255, 0.1);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
  display: inline-block;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.queued {
  background-color: #cce7ff;
  color: #0056b3;
}

.status-badge.running {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.passed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.progress-info {
  font-size: 14px;
  color: #6c757d;
}

.status-message {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
}

.error-message {
  font-size: 14px;
  color: #721c24;
  background-color: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 11px;
}

.log-level.info {
  color: #17a2b8;
}

.log-level.success {
  color: #28a745;
}

.log-level.warning {
  color: #ffc107;
}

.log-level.error {
  color: #dc3545;
}

.log-message {
  flex: 1;
  color: #495057;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.logs-actions {
  margin-top: 16px;
  text-align: right;
}

.clear-logs-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-button:hover {
  background-color: #5a6268;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
}

.no-video {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #9ca3af;
  padding: 20px;
}

.no-video p {
  margin: 8px 0;
}

.no-video-info {
  font-size: 14px;
  color: #6b7280;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #1e1e1e;
  color: #e5e7eb;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.queued {
  background-color: #cce7ff;
  color: #0056b3;
}

.status-badge.running {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.progress-info {
  font-size: 14px;
  color: #6c757d;
}

.status-message {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
}

.error-message {
  font-size: 14px;
  color: #721c24;
  background-color: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 11px;
}

.log-level.info {
  color: #17a2b8;
}

.log-level.success {
  color: #28a745;
}

.log-level.warning {
  color: #ffc107;
}

.log-level.error {
  color: #dc3545;
}

.log-message {
  flex: 1;
  color: #495057;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.logs-actions {
  margin-top: 16px;
  text-align: right;
}

.clear-logs-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-button:hover {
  background-color: #5a6268;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
}

.no-video {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #9ca3af;
  padding: 20px;
}

.no-video p {
  margin: 8px 0;
}

.no-video-info {
  font-size: 14px;
  color: #6b7280;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #1e1e1e;
  color: #e5e7eb;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}/* Test Status and Logs Styles */
.test-status {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.queued {
  background-color: #cce7ff;
  color: #0056b3;
}

.status-badge.running {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.progress-info {
  font-size: 14px;
  color: #6c757d;
}

.status-message {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
}

.error-message {
  font-size: 14px;
  color: #721c24;
  background-color: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 11px;
}

.log-level.info {
  color: #17a2b8;
}

.log-level.success {
  color: #28a745;
}

.log-level.warning {
  color: #ffc107;
}

.log-level.error {
  color: #dc3545;
}

.log-message {
  flex: 1;
  color: #495057;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.logs-actions {
  margin-top: 16px;
  text-align: right;
}

.clear-logs-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-button:hover {
  background-color: #5a6268;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
}

.no-video {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #9ca3af;
  padding: 20px;
}

.no-video p {
  margin: 8px 0;
}

.no-video-info {
  font-size: 14px;
  color: #999;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #09f;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.view-video-button {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-left: 8px;
}

.view-video-button:hover {
  background-color: #218838;
}

/* History entry styles */
.history-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-logs {
  background-color: #007bff;
  color: white;
}

.view-logs:hover {
  background-color: #0069d9;
}

.view-video {
  background-color: #28a745;
  color: white;
}

.view-video:hover {
  background-color: #218838;
}

.icon {
  font-size: 14px;
}
/* Improved tab styling */
.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
  background-color: #f9fafb;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  overflow: hidden;
}

.tab {
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  color: #374151;
  background-color: #f3f4f6;
}

.tab.active {
  color: #10b981;
  background-color: white;
  border-bottom: 2px solid #10b981;
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #10b981;
}

.file-upload-container {
  position: relative;
  width: 100%;
}

.file-upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.file-upload-area:hover {
  border-color: #007bff;
  background-color: #f0f8ff;
}

.file-upload-area.dragover {
  border-color: #007bff;
  background-color: #e6f3ff;
  border-style: solid;
  transform: scale(1.02);
}

.file-upload-area.has-file {
  border-color: #28a745;
  background-color: #f0fff0;
}

.file-upload-text {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
  font-weight: 500;
}

.file-upload-subtext {
  color: #999;
  font-size: 12px;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  top: 0;
  left: 0;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}

.upload-progress span {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.file-upload-area.uploading {
  pointer-events: none;
  opacity: 0.8;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.selected-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  font-size: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 16px;
}

.file-name {
  font-weight: 500;
  color: #333;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: #666;
  font-size: 12px;
}

.remove-file {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.remove-file:hover {
  background-color: #f8f9fa;
}

/* Mobile App Selection Styles */
.app-file-selection {
  display: flex;
  gap: 8px;
  align-items: center;
}

.app-file-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: #f9fafb;
  color: #6b7280;
  font-size: 14px;
  cursor: not-allowed;
}

.select-app-button {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.select-device-button {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #666;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
  padding: 5px;
  background-color: #f8d7da;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

/* Animation for drag over effect */
.file-upload-area.dragover {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .file-upload-area {
    padding: 15px;
    min-height: 50px;
  }
  
  .file-upload-text {
    font-size: 13px;
  }
  
  .file-upload-subtext {
    font-size: 11px;
  }
  
  .file-name {
    max-width: 150px;
  }
}
</style>
