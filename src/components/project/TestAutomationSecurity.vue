<script setup lang="ts">
import { computed,ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import { TestCase } from '../../types';
import { TestWebSocketClient } from '../../utils/testWebSocketClient';

// Generate unique action ID for multiple actions per step
const generateActionId = () => {
  return `action-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

// Utility function to convert multiple actions to execution steps
const convertToExecutionSteps = (automationSteps: any[]) => {
  const executionSteps: any[] = [];
  let stepCounter = 1;

  // If no automation steps, return empty array
  if (!automationSteps || automationSteps.length === 0) {
    console.log('No automation steps found, returning empty execution steps');
    return executionSteps;
  }

  automationSteps.forEach(step => {
    if (step.actions && step.actions.length > 0) {
      // Convert each action into a separate execution step
      step.actions.forEach((action: any, actionIndex: number) => {
        const stepName = step.actions.length > 1
          ? `${step.stepName} (Action ${actionIndex + 1})`
          : step.stepName;

        const executionStep: any = {
          step: stepCounter++,
          stepName: stepName,
          action: action.action,
          target: action.target || '',
          value: action.value || '',
          prompt: action.action === 'prompt' ? action.value : '',
          // Add metadata to track original step
          originalStep: step.step,
          actionIndex: actionIndex,
          totalActionsInStep: step.actions.length
        };

        // For upload actions, include file metadata if available
        if (action.action === 'upload') {
          // Check if file metadata is stored at step level (legacy) or action level (new)
          if (action.fileUrl && action.fileId) {
            executionStep.fileUrl = action.fileUrl;
            executionStep.fileId = action.fileId;
          } else if (step.fileUrl && step.fileId) {
            executionStep.fileUrl = step.fileUrl;
            executionStep.fileId = step.fileId;
          }
        }

        executionSteps.push(executionStep);
      });
    } else if (step.Actions) {
      // Handle new format with Actions JSON field
      try {
        const parsedActions = JSON.parse(step.Actions);
        if (parsedActions && parsedActions.length > 0) {
          parsedActions.forEach((action: any, actionIndex: number) => {
            const stepName = parsedActions.length > 1
              ? `${step.stepName} (Action ${actionIndex + 1})`
              : step.stepName;

            const executionStep: any = {
              step: stepCounter++,
              stepName: stepName,
              action: action.action,
              target: action.target || '',
              value: action.value || '',
              prompt: action.action === 'prompt' ? action.value : '',
              originalStep: step.step,
              actionIndex: actionIndex,
              totalActionsInStep: parsedActions.length
            };

            // For upload actions, include file metadata
            if (action.action === 'upload') {
              if (action.fileUrl && action.fileId) {
                executionStep.fileUrl = action.fileUrl;
                executionStep.fileId = action.fileId;
              } else if (step.fileUrl && step.fileId) {
                executionStep.fileUrl = step.fileUrl;
                executionStep.fileId = step.fileId;
              }
            }

            executionSteps.push(executionStep);
          });
        }
      } catch (error) {
        console.error('Failed to parse Actions for step:', step.step, error);
      }
    } else if (step.action) {
      // Handle legacy single action format
      const executionStep: any = {
        step: stepCounter++,
        stepName: step.stepName,
        action: step.action,
        target: step.target || '',
        value: step.value || '',
        prompt: step.prompt || '',
        originalStep: step.step,
        actionIndex: 0,
        totalActionsInStep: 1
      };

      // For upload actions, include file metadata if available
      if (step.action === 'upload' && step.fileUrl && step.fileId) {
        executionStep.fileUrl = step.fileUrl;
        executionStep.fileId = step.fileId;
      }

      executionSteps.push(executionStep);
    } else {
      // Step with no actions - skip it instead of creating placeholder
      console.log(`Skipping step ${step.step} (${step.stepName}) - no actions defined`);
    }
  });

  return executionSteps;
};

// Add a new action to a step
const addActionToStep = (step: any) => {
  if (!step.actions) {
    step.actions = [];
  }

  step.actions.push({
    id: generateActionId(),
    action: '',
    target: '',
    value: ''
  });
};

// Remove an action from a step
const removeActionFromStep = (step: any, actionId: string) => {
  if (step.actions) {
    step.actions = step.actions.filter((action: any) => action.id !== actionId);
  }
};

// Set action for a specific action within a step
const setActionForStepAction = (stepAction: any, actionType: string) => {
  stepAction.action = actionType;

  // Set default values based on action
  if (actionType === 'goto') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'click') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'write') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'pause') {
    stepAction.target = '';
    stepAction.value = '1'; // Default to 1 second
  } else if (actionType === 'upload') {
    stepAction.target = 'input[type="file"]'; // Default file input selector
    stepAction.value = '';
  } else if (actionType === 'assertText') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'assertUrl') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'assertVisible') {
    stepAction.target = '';
    stepAction.value = '';
  } else if (actionType === 'prompt') {
    stepAction.target = '';
    stepAction.value = '';
  }
};

// Handle file selection for individual actions in multiple actions
const handleActionFileSelect = async (step: any, action: any, event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) return;

  try {
    // Set uploading state
    action.uploading = true;

    // Upload file immediately to backend
    const formData = new FormData();
    formData.append('file', file);
    formData.append('stepId', step.step.toString());
    formData.append('testCaseId', selectedTestCase.value?.id || '');

    const response = await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${tcId}/automation/upload-file`,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' }
      }
    );

    // Store the uploaded file metadata in the action
    action.value = file.name;
    action.fileUrl = response.data.fileUrl;
    action.fileId = response.data.fileId;

    // Also store at step level for backward compatibility
    step.fileUrl = response.data.fileUrl;
    step.fileId = response.data.fileId;

    console.log('File uploaded successfully for action:', response.data);
  } catch (error) {
    console.error('Failed to upload file for action:', error);
    action.fileError = 'Failed to upload file. Please try again.';
  } finally {
    action.uploading = false;
  }
};

// Handle file removal for individual actions
const handleActionFileRemove = async (step: any, action: any) => {
  console.log('🗑️ handleActionFileRemove called', { step: step.step, action: action.action, fileId: action.fileId, stepFileId: step.fileId });

  if (!action.fileId && !step.fileId) {
    console.log('No file ID found to remove');
    alert('No file ID found to remove');
    return;
  }

  // Use action.fileId if available, otherwise fall back to step.fileId
  const fileIdToDelete = action.fileId || step.fileId;

  try {
    // Set removing state
    action.removing = true;

    console.log('Removing file with ID:', fileIdToDelete);

    // Call delete endpoint with the correct file ID
    await axios.delete(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${tcId}/automation/files/${fileIdToDelete}`
    );

    // Clear file metadata from action
    action.value = '';
    action.fileUrl = '';
    action.fileId = '';

    // Clear file metadata from step level
    step.fileUrl = '';
    step.fileId = '';
    step.selectedFile = null;

    // Clear any error messages
    action.fileError = '';
    step.fileError = '';

    console.log('File removed successfully for action');
  } catch (error) {
    console.error('Failed to remove file for action:', error);
    action.fileError = 'Failed to remove file. Please try again.';
  } finally {
    action.removing = false;
  }
};

interface TestExecutionRequest {
  testCaseId: string;
  tcId: string;
  projectId: string;
  steps: any[];
  testCase: {
    title: string;
    precondition?: string;
    expectation?: string;
  };
  clientId?: string;
}

const route = useRoute();
const projectId = route.params.id as string;
const tcId = route.params.tcId as string;
const selectedTestCase = ref<TestCase | null>(null);
const automationSteps = ref<any[]>([]);
const isEditing = ref(false);

// Computed property to check if automation has valid actions
const hasValidActions = computed(() => {
  return automationSteps.value.some(step => {
    // Check if step has actions in the new format
    if (step.actions && step.actions.length > 0) {
      return step.actions.some((action: any) => action.action && action.action.trim() !== '');
    }
    // Check if step has action in the legacy format
    if (step.action && step.action.trim() !== '') {
      return true;
    }
    return false;
  });
});

// Test execution state for logs display
const testProgress = ref({
  status: 'idle',
  logs: [] as string[],
  error: '',
  message: '',
  currentStep: 0,
  totalSteps: 0
});

// AgentQ API key state
const agentqApiKey = ref<string | null>(null);
const testRunning = ref(false);
const isRunning = ref(false);
const queueStatus = ref({ active: 0, waiting: 0 });

// WebSocket client and polling variables
let wsClient: any = null;
let currentClientId: string | null = null;
let pollingInterval: NodeJS.Timeout | null = null;

// Active tab state for the execution panel
const activeTab = ref('logs');

// Function to parse steps from the test case response
const parseStepsFromTestCase = (testCase: TestCase) => {
  const steps: any[] = [];

  // Add precondition as the first step if it exists
  if (testCase.precondition) {
    steps.push({
      step: 1,
      stepName: testCase.precondition.trim(),
      // Explicitly initialize clean state for new steps
      actions: [],
      fileUrl: null,
      fileId: null,
      selectedFile: null,
      fileError: '',
      uploading: false,
      isDragOver: false
    });
  }

  // Add the rest of the steps
  if (testCase.steps) {
    // Map each line to an automation step
    testCase.steps.split('\n').forEach(step => {
      // Default values with explicit clean state
      const stepObj: any = {
        step: steps.length + 1, // Start numbering after the precondition step
        stepName: step.trim(),
        // Explicitly initialize clean state for new steps
        actions: [],
        fileUrl: null,
        fileId: null,
        selectedFile: null,
        fileError: '',
        uploading: false,
        isDragOver: false
      };

      steps.push(stepObj);
    });
  }

  // Add expectation as the last step if it exists
  if (testCase.expectation) {
    const stepObj: any = {
      step: steps.length + 1,
      stepName: testCase.expectation.trim(),
      // Explicitly initialize clean state for new steps
      actions: [],
      fileUrl: null,
      fileId: null,
      selectedFile: null,
      fileError: '',
      uploading: false,
      isDragOver: false
    };

    steps.push(stepObj);
  }

  return steps;
};

// Function to change the active tab
const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

// Function to fetch AgentQ API key from backend
const fetchAgentQApiKey = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/api-keys`
    );

    if (response.data && response.data.length > 0) {
      // Find the AgentQ API key
      const agentqKey = response.data.find((key: any) => key.provider === 'agentq');
      if (agentqKey) {
        agentqApiKey.value = agentqKey.apiKey;
        console.log('AgentQ API key fetched successfully');
        return agentqKey.apiKey;
      } else {
        console.error('No AgentQ API key found');
        testProgress.value.logs.push('⚠️ No AgentQ API key found in backend');
        return null;
      }
    } else {
      console.error('No API keys found');
      testProgress.value.logs.push('⚠️ No API keys configured in backend');
      return null;
    }
  } catch (error) {
    console.error('Failed to fetch AgentQ API key:', error);
    testProgress.value.logs.push('❌ Failed to fetch AgentQ API key from backend');
    return null;
  }
};

const fetchAutomationSteps = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation`
    );

    if (response.data && response.data.steps && response.data.steps.length > 0) {
      // Use the saved automation steps and restore multiple actions if they exist
      automationSteps.value = response.data.steps.map((step: any) => {
        const processedStep = { ...step };

        // If step has Actions field (new format), parse it back to actions array
        if (step.Actions) {
          try {
            const parsedActions = JSON.parse(step.Actions);
            processedStep.actions = parsedActions.map((action: any) => {
              const restoredAction: any = {
                id: generateActionId(),
                action: action.action,
                target: action.target || '',
                value: action.value || ''
              };

              // For upload actions, restore file metadata to both action and step level
              if (action.action === 'upload' && action.fileUrl && action.fileId) {
                // Store file metadata in the action object for UI access
                restoredAction.fileUrl = action.fileUrl;
                restoredAction.fileId = action.fileId;

                // Also store at step level for backward compatibility
                processedStep.fileUrl = action.fileUrl;
                processedStep.fileId = action.fileId;

                // Create a mock selectedFile object for UI display
                if (action.value) {
                  processedStep.selectedFile = {
                    name: action.value,
                    type: 'application/octet-stream' // Default type
                  };
                }
                console.log(`Restored file metadata for upload action in step ${step.step}: ${action.fileUrl}`);
              }

              return restoredAction;
            });
            console.log(`Restored ${processedStep.actions.length} actions for step ${step.step}`);
          } catch (error) {
            console.error('Failed to parse Actions for step:', step.step, error);
            // Fallback to empty actions array if parsing fails
            processedStep.actions = [];
          }
        } else if (step.multipleActions) {
          // Handle old multipleActions format for backward compatibility
          try {
            processedStep.actions = JSON.parse(step.multipleActions);
            console.log(`Restored ${processedStep.actions.length} actions for step ${step.step} (legacy format)`);
          } catch (error) {
            console.error('Failed to parse multipleActions for step:', step.step, error);
            processedStep.actions = [];
          }
        } else if (step.action) {
          // Convert single action to actions array for consistency
          processedStep.actions = [{
            id: generateActionId(),
            action: step.action,
            target: step.target || '',
            value: step.value || ''
          }];
        } else {
          // No actions found
          processedStep.actions = [];
        }

        // Preserve file metadata for upload actions
        if (step.fileUrl && step.fileId) {
          processedStep.fileUrl = step.fileUrl;
          processedStep.fileId = step.fileId;
          console.log(`Restored file metadata for step ${step.step}: ${step.fileUrl}`);
        }

        return processedStep;
      });
      console.log('Loaded saved automation steps:', automationSteps.value);
    } else if (selectedTestCase.value) {
      // Generate steps from the test case if no saved automation exists
      automationSteps.value = parseStepsFromTestCase(selectedTestCase.value);
      console.log('Generated automation steps from test case:', automationSteps.value);
    }
  } catch (error) {
    console.error('Failed to fetch automation steps:', error);
    // Fall back to generating steps from the test case
    if (selectedTestCase.value) {
      automationSteps.value = parseStepsFromTestCase(selectedTestCase.value);
    }
  }
};

const fetchTestCaseDetails = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}`
    );

    if (response.data) {
      selectedTestCase.value = response.data;
      console.log('Found test case:', response.data);

      // Fetch automation steps after getting the test case
      await fetchAutomationSteps();
    } else {
      console.error('Test case not found with tcId:', tcId);
    }
  } catch (error) {
    console.error('Failed to fetch test case details:', error);
  }
};

// Utility functions
const generateUniqueClientId = () => {
  return `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

const checkQueueStatus = async (clientId?: string) => {
  try {
    const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_SECURITY_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3023';

    // Use CLIENT_ID-specific endpoint if clientId is provided, otherwise use global stats
    const endpoint = clientId ? `/api/queue/stats/${clientId}` : '/api/queue/stats';
    const response = await axios.get(`${wsServerUrl}${endpoint}`);

    if (response.data && response.data.success) {
      const stats = clientId ? response.data.data.client : response.data.data;
      queueStatus.value = stats;

      if (clientId) {
        console.log(`📊 CLIENT_ID-specific queue stats for ${clientId}:`, stats);
      }

      return stats;
    }
  } catch (error) {
    console.error('Failed to check queue status:', error);
  }
  return null;
};

const checkTestCaseRunning = async (testCaseId: string) => {
  try {
    const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_SECURITY_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3023';
    const response = await axios.get(`${wsServerUrl}/api/queue/running-test-cases`);

    if (response.data && response.data.success) {
      const runningTestCases = response.data.data || [];
      return runningTestCases.includes(testCaseId);
    }
  } catch (error) {
    console.error('Failed to check running test cases:', error);
  }
  return false;
};

const addLogEntry = (message: string) => {
  const cleanedMessage = cleanLogForDisplay(message);
  testProgress.value.logs.push(cleanedMessage);
};

const cleanLogForDisplay = (message: string) => {
  if (!message) return '';

  // Remove ANSI escape codes
  let cleaned = message.replace(/\x1b\[[0-9;]*m/g, '');

  // Remove excessive whitespace
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  return cleaned;
};

const startTestCompletionPolling = (clientId: string) => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
  }

  console.log('🔄 Starting immediate test completion polling for:', clientId);

  let pollCount = 0;
  const maxPolls = 24; // Maximum 2 minutes of polling (24 * 5 seconds)

  // Immediate first check
  checkTestCompletion();

  pollingInterval = setInterval(async () => {
    await checkTestCompletion();
  }, 5000); // Poll every 5 seconds

  async function checkTestCompletion() {
    pollCount++;

    try {
      console.log(`� Checking test completion (attempt ${pollCount}/${maxPolls})...`);

      // Primary: Check database for recent results
      try {
        const backendResponse = await axios.get(
          `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/test-case/${selectedTestCase.value?.id}`
        );

        if (backendResponse.data && backendResponse.data.length > 0) {
          const latestResult = backendResponse.data[0];
          const resultTime = new Date(latestResult.createdAt).getTime();
          const testStartTime = new Date().getTime() - 300000; // 5 minutes ago

          // If we have a recent result, the test completed
          if (resultTime > testStartTime) {
            console.log('✅ Found recent test completion:', latestResult.status);

            stopTestCompletionPolling();
            testRunning.value = false;
            isRunning.value = false;

            testProgress.value.status = latestResult.status;
            testProgress.value.message = latestResult.status === 'passed' ? 'Test completed successfully' : 'Test completed';

            const completionMessage = latestResult.status === 'passed' ? '✅ Test completed successfully' : '⚠️ Test completed with issues';
            testProgress.value.logs.push(completionMessage);

            // ALWAYS generate ZAP security report for security tests (regardless of pass/fail)
            testProgress.value.logs.push('📊 Generating ZAP security report...');
            testProgress.value.logs.push('🔒 Security analysis is independent of test result');

            setTimeout(() => {
              fetchZapReport();
            }, 2000);

            // Merge detailed logs
            await mergeDetailedLogs();
            return true; // Completion found
          }
        }
      } catch (dbError) {
        console.error('Error checking test completion:', dbError);
      }

      // Secondary: Check CLIENT_ID-specific queue stats
      try {
        const clientStats = await checkQueueStatus(currentClientId || undefined);

        if (clientStats) {
          console.log('📊 CLIENT_ID-specific queue stats:', clientStats);

          // If CLIENT_ID-specific queue shows no active jobs, force completion check
          if (clientStats.active === 0 && (testProgress.value.status === 'running' || testProgress.value.status === 'queued')) {
            console.log('📊 Queue shows no active jobs, forcing ZAP report generation...');

            stopTestCompletionPolling();
            testRunning.value = false;
            isRunning.value = false;

            testProgress.value.status = 'completed';
            testProgress.value.message = 'Test execution completed';
            testProgress.value.logs.push('📊 Test execution detected as complete');
            testProgress.value.logs.push('📊 Generating ZAP security report...');
            testProgress.value.logs.push('🔒 Security analysis is independent of test result');

            setTimeout(() => {
              fetchZapReport();
            }, 2000);

            await mergeDetailedLogs();
            return true; // Completion forced
          }
        }
      } catch (queueError) {
        console.error('Error checking queue stats:', queueError);
      }

      // Timeout after maximum attempts
      if (pollCount >= maxPolls) {
        console.log('⏰ Maximum polling attempts reached, forcing completion...');
        stopTestCompletionPolling();
        testRunning.value = false;
        isRunning.value = false;

        testProgress.value.status = 'completed';
        testProgress.value.message = 'Test execution completed';
        testProgress.value.logs.push('⏰ Test execution timeout reached');
        testProgress.value.logs.push('📊 Generating ZAP security report...');
        testProgress.value.logs.push('🔒 Security analysis will proceed regardless');

        setTimeout(() => {
          fetchZapReport();
        }, 2000);

        await mergeDetailedLogs();
        return true; // Timeout reached
      }

      return false; // Continue polling

    } catch (error) {
      console.error('Polling error:', error);
      return false;
    }
  }
};

const stopTestCompletionPolling = () => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
  }
};

const forceStopTest = () => {
  console.log('🛑 Force stopping security test execution');
  testProgress.value.logs.push('🛑 Security test execution manually stopped');
  testProgress.value.status = 'failed';
  testProgress.value.message = 'Security test execution stopped by user';
  testRunning.value = false;
  isRunning.value = false;

  if (wsClient) {
    wsClient.disconnect();
  }

  stopTestCompletionPolling();
};

const mergeDetailedLogs = async () => {
  try {
    // Fetch detailed logs from the backend
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/test-results`
    );

    if (response.data && response.data.length > 0) {
      const latestResult = response.data[0];
      if (latestResult.logs) {
        // Parse and merge the detailed logs
        const detailedLogs = JSON.parse(latestResult.logs);
        if (Array.isArray(detailedLogs) && detailedLogs.length > 0) {
          testProgress.value.logs.push('📋 Detailed execution logs:');
          detailedLogs.forEach(log => {
            if (log && typeof log === 'string') {
              addLogEntry(log);
            }
          });
        }
      }
    }
  } catch (error) {
    console.error('Failed to merge detailed logs:', error);
  }
};

const clearLogs = () => {
  testProgress.value.logs = [];
};

// Add retry tracking to prevent infinite loops
const zapReportRetryCount = ref(0);
const MAX_ZAP_REPORT_RETRIES = 12; // Maximum 12 retries (1 minute with 5-second intervals)

const fetchZapReport = async () => {
  try {
    zapReportRetryCount.value++;
    testProgress.value.logs.push(`🔍 Fetching ZAP security report... (attempt ${zapReportRetryCount.value}/${MAX_ZAP_REPORT_RETRIES})`);

    // Fetch ZAP report through backend API to avoid CORS issues
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/security/zap-report`,
      {
        params: {
          testCaseId: selectedTestCase.value?.id
        },
        timeout: 10000 // 10 second timeout
      }
    );

    if (response.data && response.data.success) {
      const reportData = response.data.data;

      // Check if this is a stored report
      if (reportData.stored) {
        testProgress.value.logs.push('📋 Stored Security Report Loaded');
        testProgress.value.logs.push(`🕒 Report from: ${new Date(reportData.timestamp).toLocaleString()}`);
        testProgress.value.logs.push('💡 This is from a previous security scan');
      } else {
        testProgress.value.logs.push('📋 Fresh ZAP Security Report Generated');
      }

      // Display scan context information (URLs scanned)
      if (reportData.scanContext) {
        testProgress.value.logs.push(`🎯 Scan Target Information:`);

        if (reportData.scanContext.targetUrl) {
          testProgress.value.logs.push(`🌐 Primary Target: ${reportData.scanContext.targetUrl}`);
        }

        if (reportData.scanContext.sites && reportData.scanContext.sites.length > 0) {
          testProgress.value.logs.push(`📍 Sites Scanned: ${reportData.scanContext.sites.length}`);
          reportData.scanContext.sites.slice(0, 10).forEach((site: string, index: number) => {
            testProgress.value.logs.push(`   ${index + 1}. ${site}`);
          });
          if (reportData.scanContext.sites.length > 10) {
            testProgress.value.logs.push(`   ... and ${reportData.scanContext.sites.length - 10} more sites`);
          }
        }

        if (reportData.scanContext.urls && reportData.scanContext.urls.length > 0) {
          testProgress.value.logs.push(`🔗 URLs Scanned: ${reportData.scanContext.urls.length} total`);
          reportData.scanContext.urls.slice(0, 5).forEach((url: string, index: number) => {
            testProgress.value.logs.push(`   ${index + 1}. ${url}`);
          });
          if (reportData.scanContext.urls.length > 5) {
            testProgress.value.logs.push(`   ... and ${reportData.scanContext.urls.length - 5} more URLs`);
          }
        }

        if (reportData.scanContext.hosts && reportData.scanContext.hosts.length > 0) {
          testProgress.value.logs.push(`🌐 Hosts Contacted: ${reportData.scanContext.hosts.join(', ')}`);
        }
      }

      // Display report summary
      if (reportData.summary) {
        // Check if we have detailed vulnerability summary
        if (reportData.summary.highRisk !== undefined || reportData.summary.totalIssues !== undefined) {
          testProgress.value.logs.push(`📊 Security Scan Summary:`);
          testProgress.value.logs.push(`🔴 High Risk: ${reportData.summary.highRisk || 0} issues`);
          testProgress.value.logs.push(`🟡 Medium Risk: ${reportData.summary.mediumRisk || 0} issues`);
          testProgress.value.logs.push(`🟢 Low Risk: ${reportData.summary.lowRisk || 0} issues`);
          testProgress.value.logs.push(`ℹ️ Informational: ${reportData.summary.informational || 0} issues`);
          testProgress.value.logs.push(`📈 Total Issues: ${reportData.summary.totalIssues || 0}`);

          // Display URLs scanned count from summary if available
          if (reportData.summary.urlsScanned !== undefined) {
            testProgress.value.logs.push(`🔍 URLs Analyzed: ${reportData.summary.urlsScanned}`);
          }
        } else if (reportData.summary.message) {
          // Handle stored report with message only
          testProgress.value.logs.push(`📊 ${reportData.summary.message}`);
          if (reportData.summary.storedAt) {
            testProgress.value.logs.push(`🕒 Report stored at: ${new Date(reportData.summary.storedAt).toLocaleString()}`);
          }
          testProgress.value.logs.push(`💡 Detailed vulnerability data not available for stored reports`);
          testProgress.value.logs.push(`🔄 Run a new security test to get detailed vulnerability analysis`);
        }
      }

      // Display individual vulnerabilities
      if (reportData.vulnerabilities && reportData.vulnerabilities.length > 0) {
        testProgress.value.logs.push('🔍 Vulnerability Details:');
        reportData.vulnerabilities.slice(0, 10).forEach((vuln: any) => {
          const riskIcon = vuln.risk === 'High' ? '🔴' : vuln.risk === 'Medium' ? '🟡' : vuln.risk === 'Low' ? '🟢' : 'ℹ️';
          testProgress.value.logs.push(`${riskIcon} ${vuln.name} (${vuln.risk})`);
        });

        if (reportData.vulnerabilities.length > 10) {
          testProgress.value.logs.push(`... and ${reportData.vulnerabilities.length - 10} more issues`);
        }
      } else if (reportData.stored) {
        // For stored reports without detailed vulnerability data
        testProgress.value.logs.push('📋 Stored security report loaded successfully');
        testProgress.value.logs.push('💡 Detailed vulnerability breakdown not available for stored reports');
      }

      if (reportData.stored) {
        testProgress.value.logs.push('✅ Stored Security Report Displayed');
        testProgress.value.logs.push('🔄 Run new security test for updated results');
      } else {
        testProgress.value.logs.push('✅ Fresh ZAP Security Scan Complete');
        testProgress.value.logs.push('📊 Report automatically generated and displayed');
      }

    } else {
      // Check if we've exceeded maximum retries
      if (zapReportRetryCount.value >= MAX_ZAP_REPORT_RETRIES) {
        testProgress.value.logs.push('❌ ZAP report generation timeout - maximum retries exceeded');
        testProgress.value.logs.push('💡 This may indicate the test completed without generating security data');
        testProgress.value.logs.push('🔄 Try running the test again or check if the target URL was accessible');

        // Reset retry count for future attempts
        zapReportRetryCount.value = 0;
        return;
      }

      testProgress.value.logs.push(`⚠️ ZAP report not yet available, retrying in 5 seconds... (${zapReportRetryCount.value}/${MAX_ZAP_REPORT_RETRIES})`);

      // Retry after 5 seconds
      setTimeout(() => {
        fetchZapReport();
      }, 5000);
    }
  } catch (error: any) {
    console.error('Error fetching ZAP report:', error);

    // Handle different types of errors
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      testProgress.value.logs.push('⏰ ZAP report request timeout - retrying...');

      // Don't count timeouts as full retries, but still respect max retries
      if (zapReportRetryCount.value < MAX_ZAP_REPORT_RETRIES) {
        setTimeout(() => {
          fetchZapReport();
        }, 5000);
      } else {
        testProgress.value.logs.push('❌ ZAP report generation timeout - maximum retries exceeded');
        zapReportRetryCount.value = 0;
      }
    } else if (error.response?.status === 400) {
      testProgress.value.logs.push('❌ ' + error.response.data.message);
      zapReportRetryCount.value = 0; // Reset on client error
    } else if (error.response?.status >= 500) {
      testProgress.value.logs.push('🔧 Server error fetching ZAP report - retrying...');

      if (zapReportRetryCount.value < MAX_ZAP_REPORT_RETRIES) {
        setTimeout(() => {
          fetchZapReport();
        }, 5000);
      } else {
        testProgress.value.logs.push('❌ ZAP report generation failed - maximum retries exceeded');
        zapReportRetryCount.value = 0;
      }
    } else {
      testProgress.value.logs.push('❌ Failed to fetch ZAP report: ' + (error as Error).message);
      zapReportRetryCount.value = 0; // Reset on other errors
    }
  }
};

const displayZapReport = (zapReport: any) => {
  testProgress.value.logs.push('📋 ZAP Security Report Generated:');

  // Display scan context information if available
  if (zapReport.scanContext) {
    if (zapReport.scanContext.targetUrl) {
      testProgress.value.logs.push(`🎯 Target: ${zapReport.scanContext.targetUrl}`);
    }

    if (zapReport.scanContext.urls && zapReport.scanContext.urls.length > 0) {
      testProgress.value.logs.push(`🔗 URLs Scanned: ${zapReport.scanContext.urls.length}`);
    }

    if (zapReport.scanContext.sites && zapReport.scanContext.sites.length > 0) {
      testProgress.value.logs.push(`📍 Sites: ${zapReport.scanContext.sites.length}`);
    }
  }

  if (zapReport.vulnerabilities) {
    testProgress.value.logs.push(`🔍 Total Vulnerabilities: ${zapReport.vulnerabilities.length}`);

    // Group by risk level
    const riskLevels = zapReport.vulnerabilities.reduce((acc: any, vuln: any) => {
      acc[vuln.risk] = (acc[vuln.risk] || 0) + 1;
      return acc;
    }, {});

    Object.entries(riskLevels).forEach(([risk, count]) => {
      const riskIcon = risk === 'High' ? '🔴' : risk === 'Medium' ? '🟡' : risk === 'Low' ? '🟢' : '⚪';
      testProgress.value.logs.push(`${riskIcon} ${risk} Risk: ${count} issues`);
    });
  }

  if (zapReport.reportUrl) {
    testProgress.value.logs.push(`📊 Full Report: ${zapReport.reportUrl}`);
  }
};





const loadStoredSecurityLogs = async () => {
  if (!selectedTestCase.value?.id) return;

  // Don't load stored logs if a test is currently running or queued
  if (testRunning.value || testProgress.value.status === 'running' || testProgress.value.status === 'queued') {
    console.log('⏸️ Skipping stored security logs loading - test is running or queued');
    return;
  }

  try {
    console.log('🔍 Loading stored security logs for test case:', selectedTestCase.value.id);

    // Check if there's a stored security report
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/security/zap-report`,
      {
        params: {
          testCaseId: selectedTestCase.value.id
        }
      }
    );

    if (response.data && response.data.success && response.data.data.stored) {
      const reportData = response.data.data;
      testProgress.value.logs.push('📊 Previous Security Scan Results:');
      testProgress.value.logs.push(`🕒 Scanned at: ${new Date(reportData.timestamp).toLocaleString()}`);
      testProgress.value.logs.push('📋 Loading stored security report...');

      // Auto-load the stored security report
      setTimeout(() => {
        fetchZapReport();
      }, 1500);

      // Update status to show there are previous results
      if (testProgress.value.status === 'idle') {
        testProgress.value.status = 'passed';
        testProgress.value.message = 'Previous security scan available';
      }
    }
  } catch (error: any) {
    // Silently handle errors for stored logs - this is optional functionality
    console.log('No stored security logs found (this is normal for new test cases)');
  }
};

const runTest = async () => {
  // Reset ZAP report retry count for new test
  zapReportRetryCount.value = 0;

  // Prevent double execution with debounce
  if (testRunning.value || testProgress.value.status === 'queued' || testProgress.value.status === 'running') {
    // Already running or queued a test, show a message
    const message = testProgress.value.status === 'queued'
      ? '⚠️ A test is already queued. Please wait for it to complete.'
      : '⚠️ A test is already running. Please wait for it to complete.';
    testProgress.value.logs.push(message);
    console.log('🚫 Preventing double test execution - current status:', testProgress.value.status);
    return;
  }

  console.log('🚀 Starting new security test execution');

  // Check BullMQ for active jobs before starting
  testProgress.value.logs.push('🔍 Checking for active test jobs...');
  const queueStats = await checkQueueStatus(); // Use global stats for initial check

  if (queueStats && queueStats.active >= 10) {
    const message = `⚠️ Cannot start test: ${queueStats.active} test(s) currently running. Maximum concurrent limit (10) reached.`;
    testProgress.value.logs.push(message);
    testProgress.value.logs.push(`📊 Queue status: ${queueStats.waiting} waiting, ${queueStats.active} active`);
    testProgress.value.logs.push('🚫 Test execution blocked due to concurrent test limit.');
    return;
  }

  // Check if this specific test case is already running
  const isTestCaseRunning = await checkTestCaseRunning(selectedTestCase.value?.id ?? '');
  if (isTestCaseRunning) {
    const message = `⚠️ Cannot start test: This test case is already running from another session.`;
    testProgress.value.logs.push(message);
    testProgress.value.logs.push('🚫 Test execution blocked to prevent concurrent execution of the same test case.');
    return;
  }

  if (queueStats && queueStats.waiting > 0) {
    testProgress.value.logs.push(`📋 ${queueStats.waiting} test(s) in queue. Your test will be queued.`);
  } else {
    testProgress.value.logs.push('✅ No active jobs found. Starting test...');
  }

  // Reset state for new test run
  testRunning.value = true;
  isRunning.value = true;
  testProgress.value = {
    status: 'running',
    message: 'Starting test...',
    logs: [],
    error: '',
    currentStep: 0,
    totalSteps: automationSteps.value.length
  };

  try {
    // Fetch AgentQ API key from backend
    // testProgress.value.logs.push('🔑 Fetching AgentQ API key...');
    const fetchedApiKey = await fetchAgentQApiKey();

    if (!fetchedApiKey) {
      testRunning.value = false;
      isRunning.value = false;
      testProgress.value.status = 'failed';
      testProgress.value.error = 'No AgentQ API key available';
      testProgress.value.logs.push('❌ Cannot proceed without AgentQ API key');
      return;
    }

    // Get user JWT token from localStorage for backend authentication
    const userJwtToken = localStorage.getItem('token');
    console.log('JWT Token from localStorage:', userJwtToken ? 'Found' : 'Not found');
    console.log('JWT Token length:', userJwtToken ? userJwtToken.length : 0);
    if (userJwtToken) {
      // testProgress.value.logs.push('🔐 Sending user JWT token to WebSocket for backend authentication');
    } else {
      testProgress.value.logs.push('⚠️ No user JWT token found - test may fail');
    }

    const wsUrl = (import.meta as any).env.VITE_WEBSOCKET_SECURITY_URL || 'ws://localhost:3023';
    testProgress.value.logs.push(`🔗 Connecting to WebSocket server: ${wsUrl}`);

    // Generate unique client ID for this test run to prevent interference
    currentClientId = generateUniqueClientId();
    testProgress.value.logs.push(`🆔 Generated unique client ID: ${currentClientId}`);

    // Create a new WebSocket client for each test run with unique client ID
    testProgress.value.logs.push('🔌 Creating WebSocket client...');
    wsClient = new TestWebSocketClient(fetchedApiKey, wsUrl, userJwtToken || undefined, currentClientId || undefined);

    // Add debug logging for all WebSocket messages
    const originalOnMessage = wsClient.onMessage;
    wsClient.onMessage = (event: any) => {
      console.log('🔍 WebSocket message received:', event);
      testProgress.value.logs.push(`📨 WebSocket: ${JSON.stringify(event).substring(0, 100)}...`);
      if (originalOnMessage) {
        originalOnMessage.call(wsClient, event);
      }
    };

    // Set up event handlers
    wsClient.onConnected = () => {
      testProgress.value.logs.push('✅ Connected to WebSocket server');
      console.log('✅ WebSocket connected successfully');
    };

    wsClient.onDisconnected = () => {
      // console.log('⚠️ WebSocket disconnected, starting polling for test completion');
      // testProgress.value.logs.push('⚠️ WebSocket connection lost');
      // Start polling for test completion if test is still running
      if (testProgress.value.status === 'running' || testProgress.value.status === 'queued') {
        // testProgress.value.logs.push('⚠️ Connection lost, monitoring test completion...');
        if (currentClientId) {
          startTestCompletionPolling(currentClientId);
        }
      }
    };

    wsClient.onTestStart = (data: any) => {
      console.log('🎯 Frontend received onTestStart:', data);
      testProgress.value.status = 'running';
      testProgress.value.message = 'Test execution started';
      testProgress.value.logs.push('🚀 Test execution started');
      testProgress.value.logs.push('🔍 Starting Playwright execution with ZAP proxy...');
      testRunning.value = true;
      isRunning.value = true;
    };

    wsClient.onTestOutput = (data: any) => {
      if (data.output) {
        // Clean and add the log entry
        addLogEntry(data.output);
        console.log('Test output:', data.output);
      }
    };

    wsClient.onTestComplete = (data: any) => {
      console.log('🎯 Frontend onTestComplete handler called with data:', data);

      // Stop polling since we received the completion message
      stopTestCompletionPolling();

      // Immediately update status to reflect final result
      // Use the actual test result status (passed/failed) directly
      const finalStatus = data.status || 'failed'; // Fallback to 'failed' if status is undefined
      console.log(`🎯 Setting testProgress.status to: ${finalStatus}`);
      testProgress.value.status = finalStatus;
      testProgress.value.message = finalStatus === 'passed' ? 'Test passed successfully' : 'Test failed';

      // Add completion message
      const completionMessage = finalStatus === 'passed' ? '✅ Test passed successfully' : '❌ Test failed';
      testProgress.value.logs.push(completionMessage);

      if (data.message) {
        testProgress.value.logs.push(data.message);
      }

      // Immediately stop running state
      console.log('🎯 Setting testRunning and isRunning to false');
      testRunning.value = false;
      isRunning.value = false;

      console.log(`🎯 Test final status: ${finalStatus}, testRunning: ${testRunning.value}, testProgress.status: ${testProgress.value.status}`);

      // ALWAYS generate ZAP security report for security tests (regardless of pass/fail)
      testProgress.value.logs.push('📊 Generating ZAP security report...');
      testProgress.value.logs.push('🔒 Security analysis is independent of test result');

      // Check for ZAP report in the response
      if (data.zapReport) {
        displayZapReport(data.zapReport);
      } else {
        // Automatically fetch ZAP report after a short delay
        setTimeout(() => {
          fetchZapReport();
        }, 2000);
      }

      // Merge detailed execution logs with current logs
      setTimeout(async () => {
        await mergeDetailedLogs();
        console.log('🔄 Detailed logs merged after WebSocket completion');
      }, 1000);
    };

    wsClient.onTestError = (data: any) => {
      console.error('Test error:', data);

      // Stop polling since we received an error
      stopTestCompletionPolling();

      testProgress.value.status = 'failed';
      testProgress.value.message = 'Test execution failed';

      // Clean the error message
      const cleanedError = cleanLogForDisplay(data.message || 'Unknown error');
      addLogEntry(`❌ Error: ${cleanedError}`);

      // Explicitly set testRunning to false
      testRunning.value = false;
      isRunning.value = false;
    };

    // Add queue-related event handlers
    wsClient.onTestQueued = (data: any) => {
      console.log('Test queued:', data);
      testProgress.value.status = 'queued';
      testProgress.value.message = data.message || 'Test queued successfully';

      if (data.position && data.position > 1) {
        testProgress.value.logs.push(`📋 Test queued at position ${data.position}`);
        testProgress.value.logs.push(`⏳ ${data.queueStats?.active || 0} test(s) currently running`);
        testProgress.value.logs.push(`🔄 Estimated wait time: ${data.position * 30} seconds`);
      } else {
        testProgress.value.logs.push('📋 Test queued and will start shortly');
      }

      // Add queue monitoring
      testProgress.value.logs.push('👀 Monitoring queue status...');

      // Start polling immediately when test is queued as a backup mechanism
      console.log('🔄 Starting backup polling for queued test');
      setTimeout(() => {
        if (currentClientId) {
          startTestCompletionPolling(currentClientId);
        }
      }, 2000); // Start polling after 2 seconds to give WebSocket a chance
    };

    wsClient.onQueueStatus = (data: any) => {
      console.log('Queue status update:', data);
      if (data.status === 'active') {
        testProgress.value.status = 'running';
        testProgress.value.message = 'Test is now running';
        testProgress.value.logs.push('🚀 Your test is now starting...');
      } else if (data.status === 'waiting') {
        testProgress.value.logs.push('⏳ Still waiting in queue...');
      } else if (data.status === 'completed') {
        // Handle completion via queue status
        console.log('🎯 Test completed via queue status');
        testRunning.value = false;
        isRunning.value = false;

        // Get the actual test result status from database after a delay
        setTimeout(async () => {
          try {
            const backendUrl = (import.meta as any).env.VITE_BACKEND_URL;
            const response = await axios.get(`${backendUrl}/temp-test-results/test-case/${selectedTestCase.value?.id}`);

            if (response.data && response.data.length > 0) {
              const latestResult = response.data[0];
              const actualStatus = latestResult.status;

              // Update status with actual result
              testProgress.value.status = actualStatus;
              testProgress.value.message = actualStatus === 'passed' ? 'Test passed successfully' : 'Test failed';

              const completionMessage = actualStatus === 'passed' ? '✅ Test passed successfully' : '❌ Test failed';
              testProgress.value.logs.push(completionMessage);

              console.log(`🎯 Queue completion: actual status is ${actualStatus}`);

              // ALWAYS generate ZAP security report for security tests (regardless of pass/fail)
              testProgress.value.logs.push('📊 Auto-generating ZAP security report...');
              testProgress.value.logs.push('🔒 Security analysis is independent of test result');
              setTimeout(() => {
                fetchZapReport();
              }, 2000);

              // Merge detailed logs
              await mergeDetailedLogs();
              console.log('🔄 Detailed logs merged after queue completion');
            }
          } catch (error) {
            console.error('Failed to get actual test result:', error);
            // Fallback to completed status
            testProgress.value.status = 'completed';
            testProgress.value.message = 'Test completed';
            testProgress.value.logs.push('✅ Test completed');
          }
        }, 2000); // Longer delay to ensure backend has processed the result
      } else if (data.status === 'failed') {
        // Handle failure via queue status
        console.log('🎯 Test failed via queue status');
        testProgress.value.status = 'failed';
        testProgress.value.message = 'Test failed';
        testProgress.value.logs.push('❌ Test failed');
        if (data.message) {
          testProgress.value.logs.push(`❌ Error: ${data.message}`);
        }
        testRunning.value = false;
        isRunning.value = false;

        // Merge detailed execution logs for failed tests
        setTimeout(async () => {
          await mergeDetailedLogs();
          console.log('🔄 Detailed logs merged after queue failure');
        }, 1000);
      }
    };

    wsClient.onError = (error: any) => {
      console.error('WebSocket error:', error);

      // Don't immediately fail if it's a connection timeout - the queue system should handle this
      if (error.includes('Connection timeout') || error.includes('Unable to establish connection')) {
        testProgress.value.status = 'queued';
        testProgress.value.message = 'Connecting to test server...';
        testProgress.value.logs.push(`⏳ ${error}`);
        testProgress.value.logs.push('🔄 Retrying connection... Your test will be queued when connection is established.');
      } else {
        testProgress.value.status = 'failed';
        testProgress.value.message = 'WebSocket error';
        testProgress.value.logs.push(`❌ Error: ${error}`);

        // Only set testRunning to false for actual failures, not connection issues
        testRunning.value = false;
        isRunning.value = false;
      }
    };

    // Wait longer for WebSocket connection to establish and stabilize
    testProgress.value.logs.push('⏳ Waiting for WebSocket connection to stabilize...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Prepare test execution request - convert multiple actions to execution steps
    const executionSteps = convertToExecutionSteps(automationSteps.value);

    // Debug: Log the execution steps to see what actions are being sent
    console.log('🔍 Execution steps being sent to WebSocket:', executionSteps);

    // Check if there are any execution steps
    if (executionSteps.length === 0) {
      console.log('⚠️ No execution steps found. Cannot run test without actions.');
      testProgress.value.logs.push('⚠️ No automation actions defined. Please add actions to your test steps before running.');
      testRunning.value = false;
      isRunning.value = false;
      testProgress.value.status = 'failed';
      return;
    }

    const testExecutionRequest: TestExecutionRequest = {
      testCaseId: selectedTestCase.value?.id ?? '',
      tcId: tcId,
      projectId: projectId, // Add the projectId from the route
      steps: executionSteps, // Use converted execution steps
      testCase: {
        title: selectedTestCase.value?.title ?? '',
        precondition: selectedTestCase.value?.precondition,
        expectation: selectedTestCase.value?.expectation
      }
    };

    // Execute the test
    wsClient.executeTest(testExecutionRequest);

  } catch (error) {
    console.error('Failed to start test execution:', error);
    testRunning.value = false;
    testProgress.value.status = 'failed';
    testProgress.value.error = error instanceof Error ? error.message : 'Unknown error';
    testProgress.value.logs.push(`❌ Failed to start test: ${testProgress.value.error}`);
  }
};

// Load test case details and API key on mount
onMounted(async () => {
  // Reset ZAP report retry count on component mount
  zapReportRetryCount.value = 0;

  await fetchTestCaseDetails();
  await fetchAgentQApiKey();

  // Load stored security logs after test case details are loaded
  setTimeout(() => {
    loadStoredSecurityLogs();
  }, 1000);
});

// Cleanup on unmount
onUnmounted(() => {
  // Future cleanup logic for security testing
});

const editTest = () => {
  isEditing.value = true;

  // For each step, initialize UI state based on existing data
  automationSteps.value.forEach(step => {
    console.log(`🔧 Initializing step ${step.step} for editing:`, {
      hasActions: !!step.actions,
      actionsLength: step.actions?.length || 0,
      hasLegacyAction: !!step.action,
      legacyAction: step.action,
      hasFileUrl: !!step.fileUrl,
      hasFileId: !!step.fileId,
      hasValue: !!step.value
    });

    // Initialize with existing values or empty strings
    step.target = step.target || '';
    step.value = step.value || '';

    // Convert existing prompts to the "prompt" action type
    if (step.prompt && !step.action) {
      step.action = 'prompt';
      step.value = step.prompt;
      step.prompt = '';
    }

    // Clean up any unsaved file metadata in actions
    if (step.actions) {
      step.actions.forEach((action: any) => {
        // Only keep file metadata for upload actions that have both fileUrl and fileId
        if (action.action === 'upload' && (!action.fileUrl || !action.fileId)) {
          console.log(`Clearing incomplete file data for action in step ${step.step}`);
          action.value = '';
          action.fileUrl = '';
          action.fileId = '';
        }
      });
    }

    // Reconstruct selectedFile object for uploaded files
    // Check both legacy single action format and new multiple actions format
    const hasUploadFileData = (step.action === 'upload' && step.fileUrl && step.fileId && step.value) ||
                             (step.actions && step.actions.some((action: any) =>
                               action.action === 'upload' && action.value && (action.fileUrl || step.fileUrl)
                             ));

    if (hasUploadFileData) {
      // For legacy single action format
      if (step.action === 'upload' && step.fileUrl && step.fileId && step.value) {
        step.selectedFile = { name: step.value, type: 'application/octet-stream' };
        console.log(`Reconstructed selectedFile for legacy upload in step ${step.step}: ${step.value}`);
      }

      // For multiple actions format - find the upload action with file data
      if (step.actions) {
        step.actions.forEach((action: any) => {
          if (action.action === 'upload' && action.value && (action.fileUrl || step.fileUrl)) {
            // Use action-level file metadata if available, otherwise fall back to step-level
            const fileUrl = action.fileUrl || step.fileUrl;
            const fileId = action.fileId || step.fileId;

            if (fileUrl && fileId) {
              step.selectedFile = { name: action.value, type: 'application/octet-stream' };
              console.log(`Reconstructed selectedFile for upload action in step ${step.step}: ${action.value}`);
            }
          }
        });
      }
    }

    // Initialize dropdown states (all closed by default)
    step.showInteractionDropdown = false;
    step.showAssertionDropdown = false;
  });
};

const saveTest = async () => {
  try {
    // Validate that at least one step has actions defined
    const hasAnyActions = automationSteps.value.some(step => {
      // Check if step has actions in the new format
      if (step.actions && step.actions.length > 0) {
        return step.actions.some((action: any) => action.action && action.action.trim() !== '');
      }
      // Check if step has action in the legacy format
      if (step.action && step.action.trim() !== '') {
        return true;
      }
      return false;
    });

    if (!hasAnyActions) {
      alert('Cannot save automation without any actions defined. Please add at least one action to your test steps.');
      return;
    }

    // Process steps to send clean payload structure
    const processedSteps = automationSteps.value.map(step => {
      const cleanStep: any = {
        step: step.step,
        stepName: step.stepName,
        showInteractionDropdown: false,
        showAssertionDropdown: false
      };

      if (step.actions && step.actions.length > 0) {
        // Clean actions array - remove IDs and only keep essential fields
        const cleanActions = step.actions.map((action: any) => {
          const cleanAction: any = {
            action: action.action,
            target: action.target || '',
            value: action.value || ''
          };

          // For upload actions, include file metadata if available
          if (action.action === 'upload') {
            // Use action-level file metadata if available, otherwise fall back to step-level
            const fileUrl = action.fileUrl || step.fileUrl;
            const fileId = action.fileId || step.fileId;

            if (fileUrl && fileId) {
              cleanAction.fileUrl = fileUrl;
              cleanAction.fileId = fileId;
            }

            // Set the filename as the value if not already set
            if (!cleanAction.value && step.selectedFile?.name) {
              cleanAction.value = step.selectedFile.name;
            }
          }

          return cleanAction;
        });

        // Store actions as JSON string in "Actions" field (capitalized as per DTO)
        cleanStep.Actions = JSON.stringify(cleanActions);
      } else if (step.action) {
        // Handle legacy single action format - convert to Actions array
        const singleAction = {
          action: step.action,
          target: step.target || '',
          value: step.value || ''
        };

        cleanStep.Actions = JSON.stringify([singleAction]);
      } else {
        // Step with no actions - empty actions array
        cleanStep.Actions = JSON.stringify([]);
      }

      return cleanStep;
    });

    // First update the test case details if they've changed
    if (selectedTestCase.value) {
      const updatedTestCase = {
        title: selectedTestCase.value.title,
        precondition: selectedTestCase.value.precondition,
        steps: selectedTestCase.value.steps,
        expectation: selectedTestCase.value.expectation
      };

      await axios.patch(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${selectedTestCase.value.id}`,
        updatedTestCase
      );
    }

    // Then save the automation steps using the processed steps
    console.log('Processed steps for backend:', processedSteps);
    const automationData = {
      testCaseId: selectedTestCase.value?.id,
      steps: processedSteps,
    };

    await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation`,
      automationData
    );
    console.log('Test automation steps saved successfully');

    // Update test case type to automation
    await axios.patch(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/type`,
      { testCaseType: 'automation' }
    );
    console.log('Test case type updated to automated');

    // Update automated by agentq to true
    await axios.patch(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation-by-agentq`,
      { automationByAgentq: true }
    );
    console.log('Test case automation by AgentQ updated to true');

    isEditing.value = false;
  } catch (error) {
    console.error('Failed to save security test:', error);
  }
};

const cancelEdit = () => {
  isEditing.value = false;
  // Reload the original test case to discard changes
  fetchTestCaseDetails();
};

// Helper function to determine log level from log message
const getLogLevel = (log: string): string => {
  const logLower = log.toLowerCase();
  if (logLower.includes('error') || logLower.includes('failed') || logLower.includes('❌')) {
    return 'error';
  } else if (logLower.includes('warn') || logLower.includes('warning') || logLower.includes('⚠️')) {
    return 'warning';
  } else if (logLower.includes('success') || logLower.includes('✅') || logLower.includes('passed')) {
    return 'success';
  } else {
    return 'info';
  }
};
</script>

<template>
  <div class="automation-container">
    <div class="automation-header">
      <h2>Security Testing (DAST) - {{ selectedTestCase?.title }}</h2>
      <div class="action-buttons">
        <template v-if="!isEditing">
          <div class="api-key-status">
            <span v-if="agentqApiKey" class="status-indicator api-connected">
              <span class="icon">🔑</span> API Key Ready
            </span>
            <span v-else class="status-indicator api-disconnected">
              <span class="icon">🔑</span> No API Key
            </span>
          </div>
          <div class="status-header" v-if="!(queueStatus.active >= 10)">
            <span v-if="testProgress.status === 'queued'" class="status-indicator queued">
              <span class="icon">📋</span> Test Queued
            </span>
            <span v-else-if="testRunning" class="status-indicator running">
              <span class="icon">🟡</span> Running Security Test...
            </span>
            <span v-else-if="testProgress.status === 'passed'" class="status-indicator passed">
              <span class="icon">✅</span> Security Test Passed
            </span>
            <span v-else-if="testProgress.status === 'failed'" class="status-indicator failed">
              <span class="icon">❌</span> Security Test Failed
            </span>
            <span v-else class="status-indicator idle">
              <span class="icon">🔒</span> Security Testing Ready
            </span>
          </div>
          <button
            class="run-button"
            :class="{ 'run-button-disabled': !hasValidActions }"
            @click="runTest"
            :disabled="testRunning || testProgress.status === 'queued' || testProgress.status === 'running' || queueStatus.active >= 10 || !hasValidActions"
            :title="!hasValidActions ? 'Add at least one action before running test' : (queueStatus.active >= 10 ? 'Queue is full. Please wait.' : 'Run security test')"
            v-if="!(queueStatus.active >= 10) && !testRunning && testProgress.status !== 'queued' && testProgress.status !== 'running'"
          >
            <span class="icon">▶️</span>
            <span>Run Security Test</span>
            <span v-if="!hasValidActions" class="warning-text">(No actions)</span>
          </button>
          <button
            class="stop-button"
            @click="forceStopTest"
            v-if="testRunning || testProgress.status === 'queued'"
          >
            <span class="icon">⏹️</span>
            <span>{{ testProgress.status === 'queued' ? 'Cancel' : 'Stop Test' }}</span>
          </button>
          <button class="edit-button" @click="editTest" :disabled="testRunning">
            <span class="icon">✏️</span> Edit
          </button>
        </template>
        <template v-else>
          <button 
            class="save-button"
            :disabled="!hasValidActions"
            :class="{ 'save-button-disabled': !hasValidActions }"
            @click="saveTest"
            :title="hasValidActions ? 'Save automation' : 'Add at least one action before saving'"
          >
            <span class="icon">💾</span> Save
            <span v-if="!hasValidActions" class="warning-text">(No actions defined)</span>
          </button>
          <button class="cancel-button" @click="cancelEdit">
            <span class="icon">❌</span> Cancel
          </button>
        </template>
      </div>
    </div>

    <div class="automation-content" :class="{ 'editing': isEditing }">
      <div class="steps-panel">
        <div v-for="step in automationSteps" :key="step.step" class="step-item">
          <div class="step-header">Step {{ step.step }}</div>
          <div class="step-details">
            <div v-if="step.stepName" class="step-name">{{ step.stepName }}</div>

            <!-- View mode - show multiple actions or single action -->
            <div v-if="!isEditing" class="step-action-details">
              <!-- Multiple Actions Display -->
              <div v-if="step.actions && step.actions.length > 0" class="multiple-actions-display">
                <div class="actions-header">
                  <strong>Actions ({{ step.actions.length }}):</strong>
                </div>
                <div v-for="(action, actionIndex) in step.actions" :key="action.id" class="action-item">
                  <div class="action-number">{{ actionIndex + 1 }}.</div>
                  <div class="action-content">
                    <div v-if="action.action === 'prompt'" class="action-content">
                      <strong>Prompt</strong>
                      <div>Value: {{ action.value }}</div>
                    </div>
                    <div v-else-if="action.action === 'goto'" class="action-content">
                      <strong>Go to Page</strong>
                      <div>URL: {{ action.target }}</div>
                    </div>
                    <div v-else-if="action.action === 'click'" class="action-content">
                      <strong>Click</strong>
                      <div>Target: {{ action.target }}</div>
                    </div>
                    <div v-else-if="action.action === 'write'" class="action-content">
                      <strong>Fill</strong>
                      <div>Target: {{ action.target }}</div>
                      <div>Value: {{ action.value }}</div>
                    </div>
                    <div v-else-if="action.action === 'pause'" class="action-content">
                      <strong>Pause</strong>
                      <div>Duration: {{ action.value }} seconds</div>
                    </div>
                    <div v-else-if="action.action === 'upload'" class="action-content">
                      <strong>Upload File</strong>
                      <div>Target: {{ action.target }}</div>
                      <div>File: {{ action.value }}</div>
                    </div>
                    <div v-else-if="action.action === 'assertText'" class="action-content">
                      <strong>Assert Text</strong>
                      <div>Target: {{ action.target }}</div>
                      <div>Expected Text: {{ action.value }}</div>
                    </div>
                    <div v-else-if="action.action === 'assertUrl'" class="action-content">
                      <strong>Assert URL</strong>
                      <div>Expected URL: {{ action.value }}</div>
                    </div>
                    <div v-else-if="action.action === 'assertVisible'" class="action-content">
                      <strong>Assert Visible</strong>
                      <div>Target: {{ action.target }}</div>
                    </div>
                    <div v-else class="action-content">
                      <strong>{{ action.action || 'Unknown Action' }}</strong>
                      <div v-if="action.target">Target: {{ action.target }}</div>
                      <div v-if="action.value">Value: {{ action.value }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Legacy Single Action Display -->
              <div v-else-if="step.action" class="single-action-display">
                <div v-if="step.action === 'prompt'" class="step-action">
                  <strong>Action: Prompt</strong>
                  <div>Value: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'goto'" class="step-action">
                  <strong>Action: Go to Page</strong>
                  <div>URL: {{ step.target }}</div>
                </div>
                <div v-else-if="step.action === 'click'" class="step-action">
                  <strong>Action: Click</strong>
                  <div>Target: {{ step.target }}</div>
                </div>
                <div v-else-if="step.action === 'write'" class="step-action">
                  <strong>Action: Fill</strong>
                  <div>Target: {{ step.target }}</div>
                  <div>Value: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'assertText'" class="step-action">
                  <strong>Action: Assert Text</strong>
                  <div>Target: {{ step.target }}</div>
                  <div>Expected Text: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'assertUrl'" class="step-action">
                  <strong>Action: Assert URL</strong>
                  <div>Expected URL: {{ step.value }}</div>
                </div>
                <div v-else-if="step.action === 'assertVisible'" class="step-action">
                  <strong>Action: Assert Visible</strong>
                  <div>Target: {{ step.target }}</div>
                </div>
              </div>

              <!-- No Actions Message -->
              <div v-else class="action-message">
                <div class="no-action-message">
                  No actions defined yet
                </div>
              </div>
            </div>

            <!-- Edit mode - Multiple Actions Interface -->
            <div v-else class="step-actions-container">
              <!-- Multiple Actions Management -->
              <div class="multiple-actions-section">
                <div class="actions-header">
                  <h4>Actions for this step:</h4>
                  <button class="add-action-button" @click="addActionToStep(step)">
                    <span class="icon">➕</span> Add Action
                  </button>
                </div>

                <!-- Display existing actions -->
                <div v-if="step.actions && step.actions.length > 0" class="actions-list">
                  <div v-for="(action, actionIndex) in step.actions" :key="action.id" class="action-item-edit">
                    <div class="action-header">
                      <span class="action-number">Action {{ actionIndex + 1 }}</span>
                      <button class="remove-action-button" @click="removeActionFromStep(step, action.id)">
                        <span class="icon">🗑️</span>
                      </button>
                    </div>

                    <!-- Action Type Selection -->
                    <div class="action-type-selection">
                      <label>Action Type:</label>
                      <select v-model="action.action" @change="setActionForStepAction(action, action.action)">
                        <option value="">Select action type</option>
                        <optgroup label="Navigation">
                          <option value="goto">Go to Page</option>
                        </optgroup>
                        <optgroup label="Interactions">
                          <option value="click">Click</option>
                          <option value="write">Fill/Type</option>
                          <option value="upload">Upload File</option>
                          <option value="pause">Pause</option>
                        </optgroup>
                        <optgroup label="Assertions">
                          <option value="assertText">Assert Text</option>
                          <option value="assertUrl">Assert URL</option>
                          <option value="assertVisible">Assert Visible</option>
                        </optgroup>
                        <optgroup label="AI">
                          <option value="prompt">Prompt</option>
                        </optgroup>
                      </select>
                    </div>

                    <!-- Action Fields based on selected action type -->
                    <div v-if="action.action === 'goto'" class="action-fields">
                      <div class="field-header">
                        <label>URL:</label>
                      </div>
                      <input v-model="action.target" type="text" placeholder="Enter URL" />
                    </div>

                    <div v-if="action.action === 'click'" class="action-fields">
                      <div class="field-header">
                        <label>Target:</label>
                      </div>
                      <input v-model="action.target" type="text" placeholder="Enter selector" />
                    </div>

                    <div v-if="action.action === 'write'" class="action-fields">
                      <div class="field-header">
                        <label>Target:</label>
                      </div>
                      <input v-model="action.target" type="text" placeholder="Enter selector" />
                      <div class="field">
                        <label>Value:</label>
                        <input v-model="action.value" type="text" placeholder="Enter value" />
                      </div>
                    </div>

                    <div v-if="action.action === 'pause'" class="action-fields">
                      <div class="field-header">
                        <label>Duration (seconds):</label>
                      </div>
                      <input v-model="action.value" type="number" placeholder="Enter pause duration in seconds" />
                    </div>

                    <div v-if="action.action === 'upload'" class="action-fields">
                      <div class="field-header">
                        <label>Target:</label>
                      </div>
                      <input v-model="action.target" type="text" placeholder="Enter file input selector (e.g., input[type='file'])" />
                      <div class="field">
                        <label>File:</label>
                        <div class="simple-file-upload">
                          <!-- Show file input only when no file is uploaded -->
                          <input
                            v-if="!action.value || (!action.fileUrl && !step.fileUrl)"
                            type="file"
                            @change="handleActionFileSelect(step, action, $event)"
                            class="file-input-simple"
                            accept="image/*,application/pdf,.doc,.docx,.txt,.mp4,.avi,.mov,.csv"
                          />

                          <!-- Upload status -->
                          <div v-if="action.uploading" class="upload-status">
                            <span>Uploading...</span>
                          </div>
                          <div v-else-if="action.removing" class="upload-status">
                            <span>Removing...</span>
                          </div>

                          <!-- Show uploaded file info when file exists -->
                          <div v-else-if="action.value && (action.fileUrl || step.fileUrl)" class="selected-file-info">
                            <span class="file-name">📎 {{ action.value }}</span>
                            <span v-if="action.fileUrl || step.fileUrl" class="file-status">✅ Uploaded</span>
                            <button
                              class="remove-file-button"
                              @click.stop="handleActionFileRemove(step, action)"
                              title="Remove file"
                              :disabled="action.removing"
                            >
                              {{ action.removing ? '⏳' : '✕' }}
                            </button>
                          </div>

                      
                          <div v-if="action.fileError" class="error-message">
                            {{ action.fileError }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-if="action.action === 'assertText'" class="action-fields">
                      <div class="field-header">
                        <label>Target:</label>
                      </div>
                      <input v-model="action.target" type="text" placeholder="Enter selector" />
                      <div class="field">
                        <label>Expected Text:</label>
                        <input v-model="action.value" type="text" placeholder="Enter expected text" />
                      </div>
                    </div>

                    <div v-if="action.action === 'assertUrl'" class="action-fields">
                      <div class="field-header">
                        <label>Expected URL:</label>
                      </div>
                      <input v-model="action.value" type="text" placeholder="Enter expected URL" />
                    </div>

                    <div v-if="action.action === 'assertVisible'" class="action-fields">
                      <div class="field-header">
                        <label>Target:</label>
                      </div>
                      <input v-model="action.target" type="text" placeholder="Enter selector" />
                    </div>

                    <div v-if="action.action === 'prompt'" class="action-fields">
                      <div class="field-header">
                        <label>Prompt:</label>
                      </div>
                      <input v-model="action.value" type="text" placeholder="Enter prompt for AI" />
                    </div>
                  </div>
                </div>

                <!-- Message when no actions -->
                <div v-else class="no-actions-message">
                  <p>No actions defined for this step. Click "Add Action" to get started.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="execution-panel">
        <div class="panel-tabs">
          <button 
            class="tab-button" 
            :class="{ active: activeTab === 'logs' }"
            @click="setActiveTab('logs')"
          >
            <span class="icon">📋</span> Security Logs
          </button>
        </div>

        <div class="tab-content">
          <div v-if="activeTab === 'logs'" class="logs-container">
            <div class="logs-header">
              <h4>Security Test Execution Logs</h4>
              <p class="logs-description">
                Real-time security testing logs and DAST scan results will appear here.
              </p>
            </div>
            <div class="logs-content">
              <!-- Log entries -->
              <div v-for="(log, index) in testProgress.logs" :key="index" class="log-entry" :class="getLogLevel(log)">
                <span class="log-timestamp">{{ new Date().toLocaleTimeString() }}</span>
                <span class="log-message">{{ log }}</span>
              </div>

              <!-- Placeholder when no logs -->
              <div v-if="testProgress.logs.length === 0 && testProgress.status === 'idle'" class="no-logs">
                <p>No security test execution logs yet. Click "Run Security Test" to start DAST scanning.</p>
              </div>

              <!-- Action buttons -->
              <div class="logs-actions">
                <button
                  v-if="testProgress.logs.length > 0"
                  class="clear-logs-button"
                  @click="clearLogs()"
                >
                  <span class="icon">🗑️</span>
                  Clear Logs
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.automation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.automation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e5e7eb;

  h2 {
    margin: 0;
    font-size: 24px;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;

    &:before {
      content: '🔒';
      font-size: 28px;
    }
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;

  .icon {
    font-size: 16px;
  }

  &.api-connected {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }

  &.api-disconnected {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
  }

  &.idle {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
  }
}

.automation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  height: calc(100vh - 200px);
}

.steps-panel {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
}

.step-item {
  background-color: white;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.step-header {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.step-name {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.step-action-details {
  margin-top: 8px;
  padding: 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #bae6fd;
}

.step-action {
  font-size: 13px;

  strong {
    color: #1e40af;
    display: block;
    margin-bottom: 4px;
  }

  div {
    color: #374151;
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.action-message {
  margin-top: 8px;
  padding: 8px;
  background-color: #fef3c7;
  border-radius: 4px;
  border: 1px solid #fbbf24;
}

.no-action-message {
  font-size: 13px;
  color: #92400e;
  font-style: italic;
}

.step-actions-container {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.step-action-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.dropdown {
  position: relative;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &.prompt-button {
    background-color: #8b5cf6;
    color: white;

    &:hover {
      background-color: #7c3aed;
    }
  }

  &.interaction-button {
    background-color: #3b82f6;
    color: white;

    &:hover {
      background-color: #2563eb;
    }
  }

  &.assertion-button {
    background-color: #10b981;
    color: white;

    &:hover {
      background-color: #059669;
    }
  }
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
  margin-top: 2px;
}

.dropdown-item {
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f3f4f6;
  }
}

.action-fields {
  margin-top: 8px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;

  label {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
  }
}

.close-field-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #374151;
  }
}

.field {
  margin-top: 8px;

  label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
  }
}

.action-fields input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;

  &:focus {
    outline: none;
    border-color: #e94560;
    box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
  }
}



.edit-button, .save-button, .cancel-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;

  .icon {
    font-size: 16px;
  }
}

.edit-button {
  background-color: #3b82f6;
  color: white;

  &:hover {
    background-color: #2563eb;
  }
}

.save-button {
  background-color: #10b981;
  color: white;

  &:hover {
    background-color: #059669;
  }
}

.save-button-disabled {
  background-color: #9ca3af !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.save-button-disabled:hover {
  background-color: #9ca3af !important;
  transform: none !important;
  box-shadow: none !important;
}

.cancel-button {
  background-color: #6b7280;
  color: white;

  &:hover {
    background-color: #4b5563;
  }
}

.run-button {
  background-color: #10b981;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: #059669;
    transform: translateY(-1px);
  }

  &:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }

  .icon {
    font-size: 16px;
  }
}

.stop-button {
  background-color: #ef4444;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  transition: all 0.2s;

  &:hover {
    background-color: #dc2626;
    transform: translateY(-1px);
  }

  .icon {
    font-size: 16px;
  }
}

.clear-logs-button {
  background-color: #6b7280;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
  transition: all 0.2s;

  &:hover {
    background-color: #4b5563;
  }

  .icon {
    font-size: 14px;
  }
}



.execution-panel {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-tabs {
  display: flex;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.tab-button {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;

  &:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  &.active {
    background-color: white;
    color: #e94560;
    border-bottom: 2px solid #e94560;
  }
}

.tab-content {
  flex: 1;
  overflow: hidden;
}

.logs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logs-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f8fafc;

  h4 {
    margin: 0 0 4px;
    font-size: 16px;
    color: #1f2937;
  }

  .logs-description {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
  }
}

.logs-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;

  .icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
  }

  p {
    margin: 8px 0;
    font-size: 16px;

    &.sub-text {
      font-size: 14px;
      color: #9ca3af;
    }
  }
}

.log-entries {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.log-entry {
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;

  &.error {
    background-color: #fef2f2;
    color: #991b1b;
    border-left: 4px solid #ef4444;
  }

  &.warning {
    background-color: #fffbeb;
    color: #92400e;
    border-left: 4px solid #f59e0b;
  }

  &.success {
    background-color: #f0fdf4;
    color: #166534;
    border-left: 4px solid #22c55e;
  }

  &.info {
    background-color: #f8fafc;
    color: #374151;
    border-left: 4px solid #6b7280;
  }
}

.log-timestamp {
  color: #6b7280;
  font-size: 11px;
  margin-right: 8px;
}

.log-message {
  flex: 1;
}

.logs-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

/* Multiple Actions Styles */
.multiple-actions-section {
  margin-top: 16px;
}

.actions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.actions-header h4 {
  margin: 0;
  color: #374151;
  font-size: 16px;
}

.add-action-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s ease;
}

.add-action-button:hover {
  background-color: #2563eb;
}

.actions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-item-edit {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9fafb;
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.action-number {
  font-weight: 600;
  color: #374151;
}

.remove-action-button {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.remove-action-button:hover {
  background-color: #dc2626;
}

.action-type-selection {
  margin-bottom: 12px;
}

.action-type-selection label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #374151;
}

.action-type-selection select {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.multiple-actions-display {
  margin-top: 8px;
}

.multiple-actions-display .actions-header {
  margin-bottom: 12px;
}

.multiple-actions-display .action-item {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.action-number {
  font-weight: 600;
  color: #3b82f6;
  min-width: 20px;
}

.action-content {
  flex: 1;
}

.action-content strong {
  color: #374151;
  display: block;
  margin-bottom: 4px;
}

.action-content div {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 2px;
}

.no-actions-message {
  text-align: center;
  padding: 32px;
  color: #6b7280;
  background-color: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
}

.simple-file-upload {
  margin-top: 8px;
}

.file-input-simple {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.selected-file-info {
  margin-top: 8px;
  padding: 8px;
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-weight: 500;
  color: #0f172a;
  flex: 1;
}

.file-status {
  color: #059669;
  font-size: 12px;
}

.remove-file-button {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.remove-file-button:hover {
  background-color: #dc2626;
}

.remove-file-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  margin-top: 8px;
  padding: 8px;
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: 4px;
  color: #dc2626;
  font-size: 14px;
}

.upload-status {
  margin-top: 8px;
  padding: 8px;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 4px;
  color: #92400e;
  font-size: 14px;
}

.replace-file-label {
  cursor: pointer;
  display: inline-block;
  padding: 6px 12px;
  background-color: #f59e0b;
  color: white;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
  border: none;
}

.replace-file-label:hover {
  background-color: #d97706;
}

.file-input-hidden {
  display: none !important;
}

@media (max-width: 1024px) {
  .automation-content {
    grid-template-columns: 1fr;
    height: auto;
  }

  .steps-panel {
    max-height: 400px;
  }

  .execution-panel {
    min-height: 500px;
  }
}
</style>
