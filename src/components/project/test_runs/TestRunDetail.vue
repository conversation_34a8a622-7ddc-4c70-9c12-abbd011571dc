
<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import { Chart, ArcElement, Tooltip, Legend } from 'chart.js';
import { Pie } from 'vue-chartjs';
import TestCaseResultHistory from './TestCaseResultHistory.vue';
import ExportTestRunModal from './ExportTestRunModal.vue';
import FilterTestResultsModal from './FilterTestResultsModal.vue';
import ActiveTestResultFilters from './ActiveTestResultFilters.vue';
import { TestWebSocketClient } from '../../../utils/testWebSocketClient';

// Utility function to convert multiple actions to execution steps (same as TestAutomation.vue)
const convertToExecutionSteps = (automationSteps: any[]) => {
  const executionSteps: any[] = [];
  let stepCounter = 1;

  // If no automation steps, return empty array
  if (!automationSteps || automationSteps.length === 0) {
    console.log('No automation steps found, returning empty execution steps');
    return executionSteps;
  }

  automationSteps.forEach(step => {
    if (step.actions && step.actions.length > 0) {
      // Convert each action into a separate execution step
      step.actions.forEach((action: any, actionIndex: number) => {
        const stepName = step.actions.length > 1
          ? `${step.stepName} (Action ${actionIndex + 1})`
          : step.stepName;

        const executionStep: any = {
          step: stepCounter++,
          stepName: stepName,
          action: action.action,
          target: action.target || '',
          value: action.value || '',
          prompt: action.action === 'prompt' ? action.value : '',
          // Add metadata to track original step
          originalStep: step.step,
          actionIndex: actionIndex,
          totalActionsInStep: step.actions.length
        };

        // For upload actions, include file metadata if available
        if (action.action === 'upload') {
          // Check if file metadata is stored at step level (legacy) or action level (new)
          if (action.fileUrl && action.fileId) {
            executionStep.fileUrl = action.fileUrl;
            executionStep.fileId = action.fileId;
          } else if (step.fileUrl && step.fileId) {
            executionStep.fileUrl = step.fileUrl;
            executionStep.fileId = step.fileId;
          }
        }

        executionSteps.push(executionStep);
      });
    } else if (step.Actions) {
      // Handle new format with Actions JSON field
      try {
        const parsedActions = JSON.parse(step.Actions);
        if (parsedActions && parsedActions.length > 0) {
          parsedActions.forEach((action: any, actionIndex: number) => {
            const stepName = parsedActions.length > 1
              ? `${step.stepName} (Action ${actionIndex + 1})`
              : step.stepName;

            const executionStep: any = {
              step: stepCounter++,
              stepName: stepName,
              action: action.action,
              target: action.target || '',
              value: action.value || '',
              prompt: action.action === 'prompt' ? action.value : '',
              originalStep: step.step,
              actionIndex: actionIndex,
              totalActionsInStep: parsedActions.length
            };

            // For upload actions, include file metadata
            if (action.action === 'upload') {
              if (action.fileUrl && action.fileId) {
                executionStep.fileUrl = action.fileUrl;
                executionStep.fileId = action.fileId;
              } else if (step.fileUrl && step.fileId) {
                executionStep.fileUrl = step.fileUrl;
                executionStep.fileId = step.fileId;
              }
            }

            executionSteps.push(executionStep);
          });
        }
      } catch (error) {
        console.error('Failed to parse Actions for step:', step.step, error);
      }
    } else if (step.action) {
      // Handle legacy single action format
      const executionStep: any = {
        step: stepCounter++,
        stepName: step.stepName,
        action: step.action,
        target: step.target || '',
        value: step.value || '',
        prompt: step.prompt || '',
        originalStep: step.step,
        actionIndex: 0,
        totalActionsInStep: 1
      };

      // For upload actions, include file metadata if available
      if (step.action === 'upload' && step.fileUrl && step.fileId) {
        executionStep.fileUrl = step.fileUrl;
        executionStep.fileId = step.fileId;
      }

      executionSteps.push(executionStep);
    } else {
      // Step with no actions - skip it instead of creating placeholder
      console.log(`Skipping step ${step.step} (${step.stepName}) - no actions defined`);
    }
  });

  return executionSteps;
};

// Register Chart.js components
Chart.register(ArcElement, Tooltip, Legend);

interface TestResult {
  id: string;
  testCaseId: string | number;
  status: 'passed' | 'failed' | 'blocked' | 'skipped' | 'untested';
  actualResult: string | null;
  executionTime: number | null; // Keep for backward compatibility
  duration: number | null; // Add duration field that backend actually returns
  notes: string | null;
  screenshotUrl: string | null;
  videoUrl: string | null;
  createdAt: string;
  updatedAt: string;
  sequence: number;
  isLatest: boolean;
  previousResultId?: string | null;
  testCase: {
    id: string | number;
    tcId: string | number;
    title: string;
    type: string;
    testCaseType: string;
    tags: Tag[];
    priority: string;
    platform: string;
    precondition: string;
    steps: string;
    expectation: string;
    automationByAgentq: boolean;
  };
}

interface TestRun {
  id: string;
  name: string;
  description: string;
  startTime: string | null;
  endTime: string | null;
  environment: string | null;
  build: string | null;
  release: string | null;
  type: string;
}

interface Tag {
  id: string;
  name: string;
}

const route = useRoute();
const router = useRouter();
const projectId = route.params.id as string;
const testRunId = route.params.testRunId as string;

const testRun = ref<TestRun | null>(null);
const currentPage = ref(1);
const itemsPerPage = ref(100);
const totalItems = ref(0);
const totalPages = ref(0);
const loading = ref(false);
const error = ref('');
const testResults = ref<TestResult[]>([]);
const showHistoryModal = ref(false);
const selectedTestResult = ref<TestResult | null>(null);
const filteredStatus = ref<string | null>(null);
const pieChart = ref<typeof Pie | null>(null);
const showExportModal = ref(false);
const searchQuery = ref('');
const selectedTestResults = ref<TestResult[]>([]);
const isBulkUpdate = ref(false);

// Sorting state
const sortField = ref('tcId');
const sortDirection = ref('asc');

// Filter modal state
const showFilterModal = ref(false);
const filters = ref({
  status: [] as string[],
  priority: [] as string[],
  tagIds: [] as string[],
  type: [] as string[]
});

// Temporary filters for the modal
const tempFilters = ref({
  status: [] as string[],
  priority: [] as string[],
  tagIds: [] as string[],
  type: [] as string[]
});

// Filter options
const statusOptions = ref(['passed', 'failed', 'blocked', 'skipped', 'untested']);
const priorityOptions = ref(['High', 'Medium', 'Low']);
const tagOptions = ref<{id: string, name: string}[]>([]);
const typeOptions = ref<string[]>([]);

const summaryData = ref({
  total: 0,
  passed: 0,
  failed: 0,
  blocked: 0,
  skipped: 0,
  untested: 0
});

// Chart data updated to use summaryData
const chartData = computed(() => ({
  labels: ['Passed', 'Failed', 'Blocked', 'Skipped', 'Untested'],
  datasets: [
    {
      backgroundColor: ['#10B981', '#EF4444', '#F59E0B', '#93C5FD', '#6B7280'],
      data: [
        summaryData.value.passed,
        summaryData.value.failed,
        summaryData.value.blocked,
        summaryData.value.skipped,
        summaryData.value.untested,
      ],
    },
  ],
}));

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right' as const,
    },
  },
};

const paginationRange = computed(() => {
  const range: number[] = [];
  const maxVisiblePages = 5;
  let start = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
  let end = Math.min(totalPages.value, start + maxVisiblePages - 1);

  if (end - start + 1 < maxVisiblePages) {
    start = Math.max(1, end - maxVisiblePages + 1);
  }

  for (let i = start; i <= end; i++) {
    range.push(i);
  }

  return range;
});

const handleChartClick = (event: MouseEvent) => {
  if (pieChart.value) {
    const activeElement = pieChart.value.getElementsAtEvent(event)[0];
    if (activeElement) {
      const status = ['passed', 'failed', 'blocked', 'skipped', 'untested'][activeElement.index];
      handleStatusClick(status);
    }
  }
};

const fetchTestRun = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}`
    );
    testRun.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch test run';
  }
};

const fetchTestResults = async () => {
  try {
    loading.value = true;

    // Build query parameters
    const params: any = {
      page: currentPage.value,
      limit: itemsPerPage.value,
      sortField: sortField.value,
      sortDirection: sortDirection.value.toUpperCase(), // Convert to uppercase for backend
      search: searchQuery.value || undefined
    };

    // Special handling for automationByAgentq field
    if (sortField.value === 'automationByAgentq') {
      // Change the sortField to the actual field name in the backend
      params.sortField = 'testCase.automationByAgentq';
    }

    // Add status filter (legacy support)
    if (filteredStatus.value) {
      params.status = filteredStatus.value;
    }

    // Add advanced filters
    if (filters.value.status.length > 0) {
      params.statusFilter = filters.value.status;
    }

    if (filters.value.priority.length > 0) {
      params.priorityFilter = filters.value.priority;
    }

    if (filters.value.tagIds.length > 0) {
      params.tagFilter = filters.value.tagIds;
    }

    if (filters.value.type.length > 0) {
      params.typeFilter = filters.value.type;
    }

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results`,
      { params }
    );

    testResults.value = response.data.results;
    totalItems.value = response.data.total;
    totalPages.value = response.data.totalPages;

    // Debug: Log the first test result to see what fields are available
    if (testResults.value.length > 0) {
      console.log('Sample test result data:', testResults.value[0]);
      console.log('Available fields:', Object.keys(testResults.value[0]));
      console.log('ExecutionTime value:', testResults.value[0].executionTime);
      console.log('Duration value:', testResults.value[0].duration);
      console.log('Final duration for display:', testResults.value[0].duration || testResults.value[0].executionTime);
      console.log('Video URL:', testResults.value[0].videoUrl);
      console.log('Screenshot URL:', testResults.value[0].screenshotUrl);

      // Check if any test results have video or screenshot URLs
      const resultsWithVideo = testResults.value.filter(r => r.videoUrl);
      const resultsWithScreenshot = testResults.value.filter(r => r.screenshotUrl);
      console.log(`Found ${resultsWithVideo.length} test results with video URLs`);
      console.log(`Found ${resultsWithScreenshot.length} test results with screenshot URLs`);

      if (resultsWithVideo.length > 0) {
        console.log('Sample video URLs:', resultsWithVideo.slice(0, 3).map(r => r.videoUrl));
      }
      if (resultsWithScreenshot.length > 0) {
        console.log('Sample screenshot URLs:', resultsWithScreenshot.slice(0, 3).map(r => r.screenshotUrl));
      }
    }

    // If the backend doesn't support sorting by automationByAgentq, sort locally
    if (sortField.value === 'automationByAgentq') {
      testResults.value.sort((a, b) => {
        const aValue = a.testCase?.automationByAgentq ? 1 : 0;
        const bValue = b.testCase?.automationByAgentq ? 1 : 0;
        
        return sortDirection.value === 'asc' 
          ? aValue - bValue 
          : bValue - aValue;
      });
    }

    // Extract unique values for filter options
    extractFilterOptions();

  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch test results';
  } finally {
    loading.value = false;
  }
};

// Extract unique values from test results for filter options
const extractFilterOptions = () => {
  // Extract unique test types
  const types = new Set<string>();
  testResults.value.forEach(result => {
    if (result.testCase?.testCaseType) {
      types.add(result.testCase.testCaseType);
    }
  });
  typeOptions.value = Array.from(types);

  // Extract unique priorities
  const priorities = new Set<string>();
  testResults.value.forEach(result => {
    if (result.testCase?.priority) {
      priorities.add(result.testCase.priority);
    }
  });
  priorityOptions.value = Array.from(priorities);

  // Extract unique tags
  const tags = new Map<string, string>();
  testResults.value.forEach(result => {
    if (result.testCase?.tags && result.testCase.tags.length > 0) {
      result.testCase.tags.forEach(tag => {
        tags.set(tag.id, tag.name);
      });
    }
  });
  tagOptions.value = Array.from(tags.entries()).map(([id, name]) => ({ id, name }));
};

const fetchSummary = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/summary`
    );
    summaryData.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch summary data';
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchTestResults();
};

const handlePageChange = async (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    await fetchTestResults();
    await fetchSummary();
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
      tableContainer.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }
};

const handleStatusClick = async (status: string | null) => {
  filteredStatus.value = filteredStatus.value === status ? null : status;

  // Clear status filter in advanced filters if using chart filter
  if (status) {
    filters.value.status = [];
  }

  currentPage.value = 1;
  await fetchTestResults();
  await fetchSummary();
};

// Handle sorting
const handleSort = (field: string) => {
  if (sortField.value === field) {
    // Toggle direction if clicking the same field
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // Default to ascending for a new sort field
    sortField.value = field;
    sortDirection.value = 'asc';
  }
  currentPage.value = 1;
  fetchTestResults();
};

// Get sort icon based on current sort state
const getSortIcon = (field: string) => {
  if (sortField.value !== field) return '↕️';
  return sortDirection.value === 'asc' ? '↑' : '↓';
};

// Open filter modal
const openFilterModal = () => {
  // Copy current filters to temp filters
  tempFilters.value = {
    status: [...filters.value.status],
    priority: [...filters.value.priority],
    tagIds: [...filters.value.tagIds],
    type: [...filters.value.type]
  };
  showFilterModal.value = true;
};

// Handle apply filters from modal component
const handleApplyFilters = (newFilters: typeof filters.value) => {
  filters.value = newFilters;

  // Clear the legacy status filter if using advanced filters
  if (newFilters.status.length > 0) {
    filteredStatus.value = null;
  }

  currentPage.value = 1;
  fetchTestResults();
  fetchSummary();
};

// Reset and apply filters
const resetAndApplyFilters = () => {
  filters.value = {
    status: [],
    priority: [],
    tagIds: [],
    type: []
  };
  tempFilters.value = {
    status: [],
    priority: [],
    tagIds: [],
    type: []
  };
  currentPage.value = 1;
  fetchTestResults();
  fetchSummary();
};

// Get active filter count
const getActiveFilterCount = () => {
  let count = 0;
  if (filters.value.status.length > 0) count += filters.value.status.length;
  if (filters.value.priority.length > 0) count += filters.value.priority.length;
  if (filters.value.tagIds.length > 0) count += filters.value.tagIds.length;
  if (filters.value.type.length > 0) count += filters.value.type.length;
  return count;
};

const goBack = () => {
  router.push(`/projects/${projectId}/test-runs`);
};

const fetchAgentQApiKey = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/api-keys`
    );
    
    // Check if we have any API keys
    if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
      console.error('No API keys found');
      return null;
    }
    
    // Find the AgentQ API key
    const agentqKey = response.data.find((key: any) => key.provider === 'agentq');
    
    if (!agentqKey || !agentqKey.apiKey) {
      console.error('No AgentQ API key found');
      return null;
    }
    
    console.log('AgentQ API key found');
    return agentqKey.apiKey;
  } catch (err) {
    console.error('Failed to fetch AgentQ API key:', err);
    return null;
  }
};

const isTestResultSelected = (result: TestResult): boolean => {
  return selectedTestResults.value.some(selected => selected.id === result.id);
};

const toggleTestResult = (result: TestResult, event: Event): void => {
  event.stopPropagation();

  if (isTestResultSelected(result)) {
    selectedTestResults.value = selectedTestResults.value.filter(selected => selected.id !== result.id);
  } else {
    selectedTestResults.value.push(result);
  }
};

const toggleAllTestResults = (): void => {
  if (selectedTestResults.value.length === testResults.value.length) {
    // If all are selected, deselect all
    selectedTestResults.value = [];
  } else {
    // Otherwise, select all
    selectedTestResults.value = [...testResults.value];
  }
};

const clearSelection = (): void => {
  selectedTestResults.value = [];
};

// Test execution state
const runningTests = ref(new Map());
const testLogs = ref(new Map());
let wsClient: TestWebSocketClient | null = null;

// Function to run automation test using BullMQ queue system
const runAutomationTest = async (result: TestResult, event: Event) => {
  event.stopPropagation();

  // Prevent running if already running
  if (runningTests.value.has(result.id)) {
    return;
  }

  try {
    // Set test as running
    runningTests.value.set(result.id, true);
    testLogs.value.set(result.id, ['🚀 Starting test execution...']);

    // Fetch AgentQ API key
    const apiKey = await fetchAgentQApiKey();

    if (!apiKey) {
      throw new Error('No AgentQ API key found');
    }

    // Get the current user's JWT token from localStorage
    const authToken = localStorage.getItem('token');

    if (!authToken) {
      addTestLog(result.id, '⚠️ Warning: No authentication token found, test results may not be saved');
    }

    // Check if queue is busy before starting
    try {
      const queueResponse = await axios.get(`${(import.meta as any).env.VITE_WEBSOCKET_TESTRUN_URL}/api/queue/busy`);
      if (queueResponse.data.data.busy) {
        addTestLog(result.id, `⏳ Queue is busy - ${queueResponse.data.data.message}`);
        addTestLog(result.id, `📊 Active jobs: ${queueResponse.data.data.active}, Waiting: ${queueResponse.data.data.waiting}`);
      }
    } catch (err) {
      console.warn('Could not check queue status:', err);
    }

    // Connect to TestRun WebSocket server (port 3022) with BullMQ integration
    const wsUrl = (import.meta as any).env.VITE_WEBSOCKET_TESTRUN_URL || 'ws://localhost:3022';

    // Create a direct WebSocket connection
    const ws = new WebSocket(wsUrl);

    // Set up event handlers for the BullMQ-enabled WebSocket
    ws.onopen = () => {
      addTestLog(result.id, '✅ Connected to test automation server');
      console.log('🔗 WebSocket connected to:', wsUrl);

      // Send authentication message with both API key and auth token
      const authMessage = {
        type: 'auth',
        token: apiKey,
        authToken: authToken // Send the user's JWT token
      };
      console.log('📤 Sending auth message:', { ...authMessage, token: '***', authToken: '***' });
      ws.send(JSON.stringify(authMessage));
    };

    ws.onclose = () => {
      addTestLog(result.id, '⚠️ Disconnected from test automation server');
      addTestLog(result.id, '🔄 Test may still be running in background - check status in a moment');

      // Don't immediately remove from running tests - the test might still be running
      // Set a timeout to check status later
      setTimeout(async () => {
        try {
          await fetchTestResults();
          await fetchSummary();
          console.log('🔄 Refreshed test results after WebSocket disconnect');
        } catch (err) {
          console.error('Error refreshing after disconnect:', err);
        }
      }, 5000); // Check status after 5 seconds
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      addTestLog(result.id, '❌ WebSocket connection error');
      addTestLog(result.id, '🔄 Test may still be running in background - check status in a moment');

      // Don't immediately remove from running tests - the test might still be running
      // Set a timeout to check status later
      setTimeout(async () => {
        try {
          await fetchTestResults();
          await fetchSummary();
          console.log('🔄 Refreshed test results after WebSocket error');
        } catch (err) {
          console.error('Error refreshing after error:', err);
        }
      }, 5000); // Check status after 5 seconds
    };

    ws.onmessage = async (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📥 Received WebSocket message:', data.type, data);

        switch (data.type) {
          case 'auth_success':
            addTestLog(result.id, '🔐 Authentication successful');

            // Fetch automation steps
            const stepsResponse = await axios.get(
              `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${result.testCase.tcId}/automation`
            );

            let automationSteps = [];
            if (stepsResponse.data && stepsResponse.data.steps) {
              automationSteps = stepsResponse.data.steps;
            }

            // Convert multiple actions to execution steps (same logic as TestAutomation.vue)
            const executionSteps = convertToExecutionSteps(automationSteps);

            // Check if there are any execution steps
            if (executionSteps.length === 0) {
              addTestLog(result.id, '⚠️ No automation actions defined for this test case');
              addTestLog(result.id, '❌ Cannot run test without automation actions');
              runningTests.value.delete(result.id);
              ws.close();
              return;
            }

            addTestLog(result.id, `🔍 Converted ${automationSteps.length} automation steps to ${executionSteps.length} execution steps`);

            // Send test execution request with all necessary information for BullMQ
            const executeMessage = {
              type: 'execute_test',
              token: apiKey,
              authToken: authToken, // Include the user's JWT token
              testCaseId: result.testCase.id,
              tcId: result.testCase.tcId.toString(),
              projectId: projectId,
              testRunId: testRunId, // Required for test runs
              steps: executionSteps, // Use converted execution steps
              testCase: {
                title: result.testCase.title,
                precondition: result.testCase.precondition,
                expectation: result.testCase.expectation,
                projectId: projectId
              }
            };
            console.log('📤 Sending execute_test message:', {
              ...executeMessage,
              token: '***',
              authToken: '***',
              steps: `${executionSteps.length} execution steps`
            });
            ws.send(JSON.stringify(executeMessage));
            break;

          case 'auth_failed':
            addTestLog(result.id, `❌ Authentication failed: ${data.message || 'Invalid credentials'}`);
            runningTests.value.delete(result.id);
            ws.close();
            break;

          case 'test_queued':
            addTestLog(result.id, `📋 Test queued at position ${data.position}`);
            if (data.queueStats) {
              addTestLog(result.id, `📊 Queue: ${data.queueStats.active} active, ${data.queueStats.waiting} waiting`);
            }
            addTestLog(result.id, data.message || 'Test has been added to the queue');
            break;

          case 'queue_status':
            switch (data.status) {
              case 'waiting':
                addTestLog(result.id, '⏳ Test is waiting in queue...');
                break;
              case 'active':
                addTestLog(result.id, '🚀 Test execution started');
                break;
              case 'completed':
                addTestLog(result.id, '✅ Test completed successfully');
                break;
              case 'failed':
                addTestLog(result.id, `❌ Test failed: ${data.error || 'Unknown error'}`);
                break;
            }
            break;

          case 'test_start':
            addTestLog(result.id, '🚀 Test execution started');
            break;

          case 'test_output':
            if (data.output) {
              addTestLog(result.id, data.output);
            }
            break;

          case 'test_complete':
            addTestLog(result.id, data.status === 'passed' ? '✅ Test completed successfully' : '❌ Test failed');

            // Refresh the UI after test completion
            try {
              await fetchTestResults();
              await fetchSummary();

              // Show success notification
              if (data.status === 'passed') {
                console.log('✅ Test completed successfully - UI refreshed');
              } else {
                console.log('❌ Test failed - UI refreshed');
              }

              // Auto-refresh the page after test completion
              setTimeout(() => {
                window.location.reload();
              }, 3000); // Refresh after 3 seconds

            } catch (err) {
              console.error('Error refreshing test results:', err);
            }

            // Mark test as completed
            runningTests.value.delete(result.id);

            // Close the WebSocket connection
            ws.close();
            break;

          case 'test_error':
            addTestLog(result.id, `❌ Error: ${data.message}`);
            runningTests.value.delete(result.id);
            ws.close();
            break;

          default:
            console.log('Received unknown message type:', data.type, data);
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    };

  } catch (err: any) {
    console.error('Error running automation test:', err);
    addTestLog(result.id, `❌ Error: ${err.message || 'Failed to run test'}`);
    runningTests.value.delete(result.id);
  }
};

// Helper function to add log entry
const addTestLog = (testId: string, log: string) => {
  if (!testLogs.value.has(testId)) {
    testLogs.value.set(testId, []);
  }
  const logs = testLogs.value.get(testId);
  logs.push(log);
  testLogs.value.set(testId, logs);
};

// Check if test is running
const isTestRunning = (testId: string) => {
  return runningTests.value.has(testId);
};

// Periodic status check for running tests
const startPeriodicStatusCheck = () => {
  setInterval(async () => {
    if (runningTests.value.size > 0) {
      try {
        await fetchTestResults();
        await fetchSummary();
        console.log('🔄 Periodic status check completed');
      } catch (err) {
        console.error('Error in periodic status check:', err);
      }
    }
  }, 10000); // Check every 10 seconds if there are running tests
};

// Get tooltip with logs for running test
const getTestRunTooltip = (testId: string) => {
  if (!testLogs.value.has(testId)) {
    return '';
  }
  
  return testLogs.value.get(testId).slice(-5).join('\n');
};

const handleRowClick = (result: TestResult, event: Event): void => {
  // If the click was on the checkbox or its container, don't open the modal
  if ((event.target as HTMLElement).closest('.checkbox-column') || 
      (event.target as HTMLElement).closest('.edit-button')) {
    return;
  }

  selectedTestResult.value = result;
  showHistoryModal.value = true;
};

const openBulkUpdateModal = (): void => {
  if (selectedTestResults.value.length === 0) return;

  // Use the first selected test result as a template
  selectedTestResult.value = selectedTestResults.value[0];

  // We'll handle the empty status in the TestCaseResultHistory component

  isBulkUpdate.value = true;
  showHistoryModal.value = true;
};

const closeHistoryModal = (): void => {
  const wasBulkUpdate = isBulkUpdate.value;

  showHistoryModal.value = false;
  isBulkUpdate.value = false;

  // If this was a bulk update, clear the selection
  if (wasBulkUpdate) {
    selectedTestResults.value = [];
  }
};

// Watch for changes in filters and search query
watch([filteredStatus, searchQuery, sortField, sortDirection, filters], () => {
  currentPage.value = 1;
  fetchTestResults();
  fetchSummary();
}, { deep: true });

onMounted(() => {
  fetchTestRun();
  fetchTestResults();
  fetchSummary();
  fetchAgentQApiKey();
  startPeriodicStatusCheck(); // Start periodic status checking
});

// Clean up WebSocket on component unmount
onUnmounted(() => {
  if (wsClient) {
    wsClient.disconnect();
    wsClient = null;
  }
});
</script>

<template>
  <div class="test-run-detail">
    <div class="header">
      <button class="back-button" @click="goBack">← Back to Test Runs</button>
      <div v-if="testRun" class="test-run-info">
        <div class="title-row">
          <h2>{{ testRun.name }}</h2>
          <span :class="['type-badge', testRun.type === 'security-dast' ? 'security' : 'general']">
            {{ testRun.type === 'security-dast' ? 'Security DAST' : 'General' }}
          </span>
        </div>
        <p class="description">{{ testRun.description }}</p>
        <div class="metadata">
          <span v-if="testRun.environment">Environment: {{ testRun.environment }}</span>
          <span v-if="testRun.build">Build: {{ testRun.build }}</span>
          <span v-if="testRun.release">Release: {{ testRun.release }}</span>
        </div>
      </div>
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-if="filteredStatus" class="filter-indicator">
      Showing only: <strong>{{ filteredStatus }}</strong> results
      <button @click="handleStatusClick(null)" class="clear-filter">
        Clear filter
      </button>
    </div>

    <div class="statistics-section">
      <div class="chart-container">
        <h3>Test Results Overview</h3>
        <div class="chart" @click="handleChartClick">
          <Pie :data="chartData" :options="chartOptions" :height="200" />
        </div>
      </div>

      <div class="summary">
        <div
          class="summary-item"
          :class="{ active: !filteredStatus }"
          @click="handleStatusClick(null)"
        >
          <span class="label">Total Tests</span>
          <span class="value">{{ summaryData.total }}</span>
        </div>
        <div
          class="summary-item passed"
          :class="{ active: filteredStatus === 'passed' }"
          @click="handleStatusClick('passed')"
        >
          <span class="label">Passed</span>
          <span class="value">{{ summaryData.passed }}</span>
        </div>
        <div
          class="summary-item failed"
          :class="{ active: filteredStatus === 'failed' }"
          @click="handleStatusClick('failed')"
        >
          <span class="label">Failed</span>
          <span class="value">{{ summaryData.failed }}</span>
        </div>
        <div
          class="summary-item blocked"
          :class="{ active: filteredStatus === 'blocked' }"
          @click="handleStatusClick('blocked')"
        >
          <span class="label">Blocked</span>
          <span class="value">{{ summaryData.blocked }}</span>
        </div>
        <div
          class="summary-item skipped"
          :class="{ active: filteredStatus === 'skipped' }"
          @click="handleStatusClick('skipped')"
        >
          <span class="label">Skipped</span>
          <span class="value">{{ summaryData.skipped }}</span>
        </div>
        <div
          class="summary-item untested"
          :class="{ active: filteredStatus === 'untested' }"
          @click="handleStatusClick('untested')"
        >
          <span class="label">Untested</span>
          <span class="value">{{ summaryData.untested }}</span>
        </div>
      </div>
    </div>

    <div class="test-results-section">
      <div class="results-header">
        <h3>Test Results</h3>
        <div class="actions-container">
          <div v-if="selectedTestResults.length > 0" class="bulk-actions">
            <button class="bulk-update-button" @click="openBulkUpdateModal">
              Update {{ selectedTestResults.length }} Selected Results
            </button>
            <button class="clear-selection-button" @click="clearSelection">
              Clear Selection
            </button>
          </div>
          <div class="search-and-filter">
            <div class="search-box">
              <input
                type="text"
                v-model="searchQuery"
                placeholder="Search by ID, title, type, priority, platform..."
                @keyup.enter="handleSearch"
              />
              <button class="search-button" @click="handleSearch">
                Search
              </button>
            </div>
            <button
              @click="openFilterModal"
              class="filter-button"
              :class="{ active: getActiveFilterCount() > 0 }"
            >
              <span class="button-icon">🔍</span> Filter
              <span v-if="getActiveFilterCount() > 0" class="filter-badge">{{ getActiveFilterCount() }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Filter Modal Component -->
      <FilterTestResultsModal
        v-model="showFilterModal"
        :status-options="statusOptions"
        :priority-options="priorityOptions"
        :tag-options="tagOptions"
        :type-options="typeOptions"
        :initial-filters="tempFilters"
        @apply="handleApplyFilters"
      />

      <!-- Active Filters Component -->
      <ActiveTestResultFilters
        :filters="filters"
        :tag-options="tagOptions"
        @clear="resetAndApplyFilters"
      />

      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="checkbox-column">
                <input
                  type="checkbox"
                  :checked="selectedTestResults.length > 0 && selectedTestResults.length === testResults.length"
                  :indeterminate="selectedTestResults.length > 0 && selectedTestResults.length < testResults.length"
                  @click="toggleAllTestResults"
                  class="checkbox"
                />
              </th>
              <th @click="handleSort('tcId')" class="sortable-header">
                ID <span class="sort-icon">{{ getSortIcon('tcId') }}</span>
              </th>
              <th @click="handleSort('title')" class="sortable-header">
                Title <span class="sort-icon">{{ getSortIcon('title') }}</span>
              </th>
              <th @click="handleSort('priority')" class="sortable-header">
                Priority <span class="sort-icon">{{ getSortIcon('priority') }}</span>
              </th>
              <th @click="handleSort('status')" class="sortable-header">
                Status <span class="sort-icon">{{ getSortIcon('status') }}</span>
              </th>
              <th @click="handleSort('automationByAgentq')" class="sortable-header">
                Action <span class="sort-icon">{{ getSortIcon('automationByAgentq') }}</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="result in testResults"
              :key="result.id"
              class="test-result-row"
              @click.prevent="handleRowClick(result, $event)"
            >
              <td class="checkbox-column" @click.stop>
                <input
                  type="checkbox"
                  :checked="isTestResultSelected(result)"
                  @click="toggleTestResult(result, $event)"
                  class="checkbox"
                />
              </td>
              <td>TC-{{ result.testCase?.tcId }}</td>
              <td>{{ result.testCase?.title }}</td>
              <td>
                {{ result.testCase?.priority }}
              </td>
              <td>
                <span :class="['status-badge', result.status]">
                  {{ result?.status }}
                </span>
              </td>
              <td>
                <button
                  v-if="!isTestRunning(result.id) && (result.testCase?.automationByAgentq && result.testCase?.type?.toLowerCase() === 'functional')"
                  class="edit-button"
                  @click="runAutomationTest(result, $event)"
                >
                  <span class="edit-icon">▶️</span> Run Test
                </button>
                <button
                  v-if="isTestRunning(result.id) && (result.testCase?.automationByAgentq && result.testCase?.type?.toLowerCase() === 'functional')"
                  class="edit-button running"
                  disabled
                  :title="getTestRunTooltip(result.id)"
                >
                  <span class="edit-icon">⏳</span> Running...
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div v-if="totalPages > 1" class="pagination">
      <button
        class="page-button"
        :disabled="currentPage === 1"
        @click="handlePageChange(currentPage - 1)"
      >
        Previous
      </button>

      <div class="page-numbers">
        <button
          v-if="paginationRange[0] > 1"
          class="page-number"
          @click="handlePageChange(1)"
        >
          1
        </button>

        <span v-if="paginationRange[0] > 2" class="ellipsis">...</span>

        <button
          v-for="page in paginationRange"
          :key="page"
          class="page-number"
          :class="{ active: currentPage === page }"
          @click="handlePageChange(page)"
        >
          {{ page }}
        </button>

        <span v-if="paginationRange[paginationRange.length - 1] < totalPages - 1" class="ellipsis">...</span>

        <button
          v-if="paginationRange[paginationRange.length - 1] < totalPages"
          class="page-number"
          @click="handlePageChange(totalPages)"
        >
          {{ totalPages }}
        </button>
      </div>

      <button
        class="page-button"
        :disabled="currentPage === totalPages"
        @click="handlePageChange(currentPage + 1)"
      >
        Next
      </button>
    </div>

    <TestCaseResultHistory
      v-if="showHistoryModal && selectedTestResult"
      :show="showHistoryModal"
      :projectId="projectId"
      :testRunId="testRunId"
      :testResultId="selectedTestResult ? selectedTestResult.id : ''"
      :testCaseTitle="selectedTestResult ? selectedTestResult.testCase.title : ''"
      :tcType="selectedTestResult ? selectedTestResult.testCase.type : ''"
      :testCaseType="selectedTestResult ? selectedTestResult.testCase.testCaseType : ''"
      :testCasePriority="selectedTestResult ? selectedTestResult.testCase.priority : ''"
      :testCasePrecondition="selectedTestResult ? selectedTestResult.testCase.precondition : ''"
      :testCaseSteps="selectedTestResult ? selectedTestResult.testCase.steps : ''"
      :testCaseExpectation="selectedTestResult ? selectedTestResult.testCase.expectation : ''"
      :tcId="selectedTestResult ? selectedTestResult.testCase.tcId : ''"
      :testCasePlatform="selectedTestResult ? selectedTestResult.testCase.platform : ''"
      :testCaseTags="selectedTestResult ? selectedTestResult.testCase.tags.map(tag => ({ ...tag, createdAt: '', updatedAt: '' })) : []"
      :testCaseStatus="selectedTestResult ? selectedTestResult.status : ''"
      :testCaseActualResult="selectedTestResult ? selectedTestResult.actualResult : ''"
      :testCaseNotes="selectedTestResult ? selectedTestResult.notes : ''"
      :testCaseScreenshotUrl="selectedTestResult ? selectedTestResult.screenshotUrl : ''"
      :testCaseVideoUrl="selectedTestResult ? selectedTestResult.videoUrl : ''"
      :testCaseExecutionTime="selectedTestResult ? (selectedTestResult.duration || selectedTestResult.executionTime) : ''"
      :testCaseCreatedAt="selectedTestResult ? selectedTestResult.createdAt : ''"
      :testCaseUpdatedAt="selectedTestResult ? selectedTestResult.updatedAt : ''"
      :isBulkUpdate="isBulkUpdate"
      :selectedTestResults="isBulkUpdate ? selectedTestResults : []"
      @close="closeHistoryModal"
      @refresh="fetchTestResults(); fetchSummary()"
    />

    <ExportTestRunModal
      v-if="showExportModal"
      @close="showExportModal = false"
    />
  </div>
</template>

<style lang="scss" scoped>
.test-run-detail {
  min-height: calc(100vh - 120px);
}

.header {
  margin-bottom: 5px;

  .back-button {
    padding: 5px 10px;
    background: none;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    color: #4b5563;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: #edf2f7;
    }
  }

  .test-run-info {
    margin-top: 24px;

    .title-row {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 12px;
    }

    h2 {
      font-size: 28px;
      color: #1e293b;
      margin: 0;
    }

    .type-badge {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.general {
        background-color: #e0f2fe;
        color: #0277bd;
        border: 1px solid #b3e5fc;
      }

      &.security {
        background-color: #fff3e0;
        color: #f57c00;
        border: 1px solid #ffcc02;
      }
    }

    .description {
      color: #64748b;
      margin-bottom: 24px;
      line-height: 1.6;
    }

    .metadata {
      display: flex;
      gap: 16px;
      color: #4a5568;
      font-size: 14px;

      span {
        padding: 8px 16px;
        background-color: #e2e8f0;
        border-radius: 8px;
      }
    }
  }
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .actions-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .bulk-actions {
    display: flex;
    gap: 8px;
  }

  .refresh-button {
    padding: 8px 16px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease-in-out;

    &:hover {
      background: #2563eb;
      transform: translateY(-1px);
    }

    .refresh-icon {
      font-size: 16px;
    }
  }

  .bulk-update-button {
    padding: 8px 16px;
    background-color: #10B981;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: #059669;
    }
  }

  .clear-selection-button {
    padding: 8px 16px;
    background-color: #f3f4f6;
    color: #4b5563;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: #e5e7eb;
    }
  }
}

.search-and-filter {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-box {
  display: flex;
  gap: 8px;
  align-items: center;

  input {
    width: 300px;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #10b981;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
    }
  }

  .search-button {
    padding: 8px 16px;
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #e5e7eb;
    }
  }
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  position: relative;

  &:hover {
    background-color: #e5e7eb;
  }

  &.active {
    background-color: #dcfce7;
    border-color: #10b981;
    color: #047857;
  }

  .button-icon {
    font-size: 14px;
  }

  .filter-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #10b981;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    font-weight: 600;
  }
}

.export-button{
  display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border: none;
    background-color: #f3f4f6;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    border-radius: 6px;

    &:hover {
      background-color: #c6c7c9;
    }
}

.error-message {
  background-color: #fef2f2;
  color: #b91c1c;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #ef4444;
}

.statistics-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
  margin-bottom: 48px;
  align-items: start;
}

.chart-container {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  h3 {
    font-size: 18px;
    color: #1e293b;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .chart {
    height: 200px;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
}

.summary {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;

  .summary-item {
    background-color: white;
    border-radius: 12px;
    padding: 10px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    gap: 12px;
    cursor: pointer;
    transition: all 0.2s;

    .label {
      font-size: 14px;
      color: #4a5568;
      font-weight: 500;
    }

    .value {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }

    &:hover {
      background-color: #f0f0f0;
    }

    &.active {
      background-color: none;
    }

    &.passed .value {
      color: #22c55e;
    }

    &.failed .value {
      color: #dc2626;
    }

    &.blocked .value {
      color: #f97316;
    }

    &.skipped .value {
      color: #93C5FD;
    }

    &.untested .value {
      color: #71717a;
    }
  }
}

.test-results-section {
  background-color: white;
  border-radius: 12px;
  padding: 10px;

  h3 {
    font-size: 18px;
    color: #1e293b;
    margin-bottom: 20px;
    font-weight: 500;
  }
}

.tags-container {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag-badge{
  display: inline-block;
      padding: 4px 8px;
      background-color: #e0f2f7; /* Example background color */
      color: #0366d6; /* Example text color */
      border-radius: 4px;
      font-size: 12px;
      margin-right: 4px;
      margin-bottom: 4px;
}

.table-container {
  overflow-x: auto;

  table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      padding: 14px;
      text-align: left;
      border-bottom: 1px solid #e2e8f0;
    }

    th {
      background-color: #f0f4f8;
      font-weight: 500;
      color: #1e293b;

      &.sortable-header {
        cursor: pointer;
        user-select: none;
        position: relative;
        padding-right: 24px;

        &:hover {
          background-color: #e2e8f0;
        }

        .sort-icon {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 14px;
          color: #64748b;
        }
      }
    }

    .checkbox-column {
      width: 40px;
      padding: 12px 8px;
      text-align: center;
    }

    .checkbox {
      width: 18px;
      height: 18px;
      cursor: pointer;
      border-radius: 4px;
      border: 2px solid #cbd5e1;
      transition: all 0.2s ease-in-out;

      &:checked {
        background-color: #3b82f6;
        border-color: #3b82f6;
      }

      &:hover {
        border-color: #3b82f6;
      }
    }
  }
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;

  &.passed {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #86efac;
  }

  &.failed {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #fca5a5;
  }

  &.blocked {
    background-color: #fff7ed;
    color: #c2410c;
    border: 1px solid #fdba74;
  }

  &.skipped {
    background-color: #eff6ff;
    color: #2563eb;
    border: 1px solid #93c5fd;
  }

  &.untested {
    background-color: #f4f4f5;
    color: #52525b;
  }
}

.form-input {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: #63b3ed;
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.2);
  }
}

.form-select {
  padding: 8px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: #63b3ed;
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.2);
  }
}

.edit-actions {
  display: flex;
  gap: 12px;
  align-items: center;

  button {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }

  .save-button {
    background-color: #f0fdf4;
    color: #059669;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #dcfce7;
    }
  }

  .cancel-button {
    color: #6b7280;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #f3f4f6;
    }
  }
}

.edit-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #e5e7eb;
  background-color: white;
  color: #059669;

  &:hover {
    background-color: #f0fdf4;
  }
}

.test-result-row {
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background-color: #f9fafb;
  }
}

.filter-indicator {
  padding: 8px 16px;
  background-color: #f9e1df;
  border-radius: 4px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.clear-filter {
  margin-left: 8px;
  background: none;
  border: none;
  cursor: pointer;
  color: #e82e2e;
}

.summary-item {
  cursor: pointer;
  transition: all 0.2s;
}

.summary-item:hover {
  transform: translateY(-2px);
}

.summary-item.active {
  box-shadow: 0 0 0 2px #e82e2e;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
  padding: 16px 0;
}

.page-button, .page-number {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.page-button:hover:not(:disabled),
.page-number:hover:not(.active) {
  background-color: #f3f4f6;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-number.active {
  background-color: #e94560;
  color: white;
  border-color: #e94560;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.ellipsis {
  padding: 8px;
  color: #6b7280;
}

/* Running test button styles */
.edit-button.running {
  background-color: #f59e0b;
  color: white;
  cursor: not-allowed;
  opacity: 0.8;
}

.edit-button.running:disabled {
  background-color: #f59e0b;
  color: white;
  opacity: 0.8;
}

.edit-button.running .edit-icon {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
