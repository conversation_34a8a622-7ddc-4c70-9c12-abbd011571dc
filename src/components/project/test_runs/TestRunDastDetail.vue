<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import { Chart, ArcElement, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js';
import { Pie, Bar } from 'vue-chartjs';
import TestCaseDastResultHistory from './TestCaseDastResultHistory.vue';
import ExportTestRunModal from './ExportTestRunModal.vue';
import FilterTestResultsModal from './FilterTestResultsModal.vue';
import ActiveTestResultFilters from './ActiveTestResultFilters.vue';

// Utility function to convert multiple actions to execution steps (same as TestAutomation.vue)
const convertToExecutionSteps = (automationSteps: any[]) => {
  const executionSteps: any[] = [];
  let stepCounter = 1;

  // If no automation steps, return empty array
  if (!automationSteps || automationSteps.length === 0) {
    console.log('No automation steps found, returning empty execution steps');
    return executionSteps;
  }

  automationSteps.forEach(step => {
    if (step.actions && step.actions.length > 0) {
      // Convert each action into a separate execution step
      step.actions.forEach((action: any, actionIndex: number) => {
        const stepName = step.actions.length > 1
          ? `${step.stepName} (Action ${actionIndex + 1})`
          : step.stepName;

        const executionStep: any = {
          step: stepCounter++,
          stepName: stepName,
          action: action.action,
          target: action.target || '',
          value: action.value || '',
          prompt: action.action === 'prompt' ? action.value : '',
          // Add metadata to track original step
          originalStep: step.step,
          actionIndex: actionIndex,
          totalActionsInStep: step.actions.length
        };

        // For upload actions, include file metadata if available
        if (action.action === 'upload') {
          // Check if file metadata is stored at step level (legacy) or action level (new)
          if (action.fileUrl && action.fileId) {
            executionStep.fileUrl = action.fileUrl;
            executionStep.fileId = action.fileId;
          } else if (step.fileUrl && step.fileId) {
            executionStep.fileUrl = step.fileUrl;
            executionStep.fileId = step.fileId;
          }
        }

        executionSteps.push(executionStep);
      });
    } else if (step.Actions) {
      // Handle new format with Actions JSON field
      try {
        const parsedActions = JSON.parse(step.Actions);
        if (parsedActions && parsedActions.length > 0) {
          parsedActions.forEach((action: any, actionIndex: number) => {
            const stepName = parsedActions.length > 1
              ? `${step.stepName} (Action ${actionIndex + 1})`
              : step.stepName;

            const executionStep: any = {
              step: stepCounter++,
              stepName: stepName,
              action: action.action,
              target: action.target || '',
              value: action.value || '',
              prompt: action.action === 'prompt' ? action.value : '',
              originalStep: step.step,
              actionIndex: actionIndex,
              totalActionsInStep: parsedActions.length
            };

            // For upload actions, include file metadata
            if (action.action === 'upload') {
              if (action.fileUrl && action.fileId) {
                executionStep.fileUrl = action.fileUrl;
                executionStep.fileId = action.fileId;
              } else if (step.fileUrl && step.fileId) {
                executionStep.fileUrl = step.fileUrl;
                executionStep.fileId = step.fileId;
              }
            }

            executionSteps.push(executionStep);
          });
        }
      } catch (error) {
        console.error('Failed to parse Actions for step:', step.step, error);
      }
    } else if (step.action) {
      // Handle legacy single action format
      const executionStep: any = {
        step: stepCounter++,
        stepName: step.stepName,
        action: step.action,
        target: step.target || '',
        value: step.value || '',
        prompt: step.prompt || '',
        originalStep: step.step,
        actionIndex: 0,
        totalActionsInStep: 1
      };

      // For upload actions, include file metadata if available
      if (step.action === 'upload' && step.fileUrl && step.fileId) {
        executionStep.fileUrl = step.fileUrl;
        executionStep.fileId = step.fileId;
      }

      executionSteps.push(executionStep);
    } else {
      // Step with no actions - skip it instead of creating placeholder
      console.log(`Skipping step ${step.step} (${step.stepName}) - no actions defined`);
    }
  });

  return executionSteps;
};

// Register Chart.js components
Chart.register(ArcElement, Tooltip, Legend, BarElement, CategoryScale, LinearScale);

interface TestResult {
  id: string;
  testCaseId: string | number;
  status: 'passed' | 'failed' | 'blocked' | 'skipped' | 'untested' | 'New' | 'Open' | 'In Progress' | 'Fixed/Remediated' | 'Verified/Re-tested' | 'Closed' | 'False Positive' | 'Accepted Risk / Waived' | 'Duplicate';
  actualResult: string | null;
  executionTime: number | null;
  duration: number | null;
  notes: string | null;
  vulnerabilityDescription: string | null;
  originalSeverity: string | null;
  adjustedSeverity: string | null;
  affectedUrls: string | null;
  requestResponse: string | null;
  remediationGuidance: string | null;
  screenshotUrl: string | null;
  videoUrl: string | null;
  logsSecurityUrl: string | null;
  highCount: number | null;
  mediumCount: number | null;
  lowCount: number | null;
  informationalCount: number | null;
  falsePositiveCount: number | null;
  createdAt: string;
  updatedAt: string;
  sequence: number;
  isLatest: boolean;
  previousResultId?: string | null;
  createdBy?: string | null;
  testCase: {
    id: string | number;
    tcId: string | number;
    title: string;
    type: string;
    testCaseType: string;
    tags: Tag[];
    priority: string;
    platform: string;
    precondition: string;
    steps: string;
    expectation: string;
    automationByAgentq: boolean;
  };
}

interface TestRun {
  id: string;
  name: string;
  description: string;
  startTime: string | null;
  endTime: string | null;
  environment: string | null;
  build: string | null;
  release: string | null;
  type: string;
}

interface Tag {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

const route = useRoute();
const router = useRouter();
const projectId = route.params.id as string;
const testRunId = route.params.testRunId as string;

const testRun = ref<TestRun | null>(null);
const currentPage = ref(1);
const itemsPerPage = ref(100);
const totalItems = ref(0);
const totalPages = ref(0);
const loading = ref(false);
const error = ref('');
const testResults = ref<TestResult[]>([]);
const showHistoryModal = ref(false);
const selectedTestResult = ref<TestResult | null>(null);
const filteredStatus = ref<string | null>(null);
const showExportModal = ref(false);
const searchQuery = ref('');
const selectedTestResults = ref<TestResult[]>([]);
const isBulkUpdate = ref(false);

// Sorting state
const sortField = ref('tcId');
const sortDirection = ref('asc');

// Filter modal state
const showFilterModal = ref(false);
const filters = ref({
  status: [] as string[],
  priority: [] as string[],
  tagIds: [] as string[],
  type: [] as string[]
});

// Temporary filters for the modal
const tempFilters = ref({
  status: [] as string[],
  priority: [] as string[],
  tagIds: [] as string[],
  type: [] as string[]
});

// Filter options
const statusOptions = ref(['passed', 'failed', 'blocked', 'skipped', 'untested']);
const priorityOptions = ref(['High', 'Medium', 'Low']);
const tagOptions = ref<{id: string, name: string}[]>([]);
const typeOptions = ref<string[]>([]);



// DAST-specific summary data
const dastSummaryData = ref({
  vulnerabilities: {
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    falsePositive: 0
  },
  executionStatus: {
    new: 0,
    open: 0,
    inProgress: 0,
    fixedRemediated: 0,
    verifiedRetested: 0,
    closed: 0,
    falsePositive: 0,
    acceptedRisk: 0,
    duplicate: 0
  },
  testCoverage: {
    totalTestCases: 0,
    executedTestCases: 0,
    coveragePercentage: 0
  },
  securityMetrics: {
    authenticationTests: 0,
    authorizationTests: 0,
    inputValidationTests: 0,
    sessionManagementTests: 0
  }
});

// Chart data for DAST vulnerabilities
const vulnerabilityChartData = computed(() => ({
  labels: ['Critical', 'High', 'Medium', 'Low', 'False Positive'],
  datasets: [
    {
      backgroundColor: ['#DC2626', '#EF4444', '#F59E0B', '#10B981', '#6B7280'],
      data: [
        dastSummaryData.value.vulnerabilities.critical,
        dastSummaryData.value.vulnerabilities.high,
        dastSummaryData.value.vulnerabilities.medium,
        dastSummaryData.value.vulnerabilities.low,
        dastSummaryData.value.vulnerabilities.falsePositive,
      ],
    },
  ],
}));



const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right' as const,
    },
  },
};

// Execution Status Stacked Bar Chart Data
const executionStatusChartData = computed(() => ({
  labels: ['Test Execution Status'],
  datasets: [
    {
      label: 'New',
      backgroundColor: '#3B82F6',
      borderColor: '#2563EB',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.new || 0],
    },
    {
      label: 'Open',
      backgroundColor: '#EF4444',
      borderColor: '#DC2626',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.open || 0],
    },
    {
      label: 'In Progress',
      backgroundColor: '#F59E0B',
      borderColor: '#D97706',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.inProgress || 0],
    },
    {
      label: 'Fixed/Remediated',
      backgroundColor: '#10B981',
      borderColor: '#059669',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.fixedRemediated || 0],
    },
    {
      label: 'Verified/Re-tested',
      backgroundColor: '#8B5CF6',
      borderColor: '#7C3AED',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.verifiedRetested || 0],
    },
    {
      label: 'Closed',
      backgroundColor: '#6B7280',
      borderColor: '#4B5563',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.closed || 0],
    },
    {
      label: 'False Positive',
      backgroundColor: '#EC4899',
      borderColor: '#DB2777',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.falsePositive || 0],
    },
    {
      label: 'Accepted Risk / Waived',
      backgroundColor: '#F97316',
      borderColor: '#EA580C',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.acceptedRisk || 0],
    },
    {
      label: 'Duplicate',
      backgroundColor: '#06B6D4',
      borderColor: '#0891B2',
      borderWidth: 1,
      data: [dastSummaryData.value.executionStatus.duplicate || 0],
    },
  ],
}));

// Horizontal Stacked Bar Chart Options
const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  indexAxis: 'y' as const,
  scales: {
    x: {
      stacked: true,
      beginAtZero: true,
      ticks: {
        stepSize: 1,
      },
    },
    y: {
      stacked: true,
      ticks: {
        font: {
          size: 12,
        },
      },
    },
  },
  plugins: {
    legend: {
      display: true,
      position: 'bottom' as const,
      labels: {
        usePointStyle: true,
        padding: 15,
        font: {
          size: 11,
        },
      },
    },
    tooltip: {
      callbacks: {
        label: function(context: any) {
          const label = context.dataset.label || '';
          const value = context.parsed.x;
          return `${label}: ${value} test case${value !== 1 ? 's' : ''}`;
        },
        footer: function(tooltipItems: any[]) {
          const total = tooltipItems.reduce((sum, item) => sum + item.parsed.x, 0);
          return `Total: ${total} test case${total !== 1 ? 's' : ''}`;
        }
      }
    }
  },
};

const goBack = () => {
  router.push(`/projects/${projectId}/test-runs`);
};

const fetchTestRun = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}`
    );
    testRun.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch test run';
  }
};

const fetchTestResults = async () => {
  try {
    loading.value = true;

    // Build query parameters
    const params: any = {
      page: currentPage.value,
      limit: itemsPerPage.value,
      sortField: sortField.value,
      sortDirection: sortDirection.value.toUpperCase(),
      search: searchQuery.value || undefined
    };

    // Special handling for automationByAgentq field
    if (sortField.value === 'automationByAgentq') {
      params.sortField = 'testCase.automationByAgentq';
    }

    // Add status filter (legacy support)
    if (filteredStatus.value) {
      params.status = filteredStatus.value;
    }

    // Add advanced filters
    if (filters.value.status.length > 0) {
      params.statusFilter = filters.value.status;
    }

    if (filters.value.priority.length > 0) {
      params.priorityFilter = filters.value.priority;
    }

    if (filters.value.tagIds.length > 0) {
      params.tagFilter = filters.value.tagIds;
    }

    if (filters.value.type.length > 0) {
      params.typeFilter = filters.value.type;
    }

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results`,
      { params }
    );

    testResults.value = response.data.results;
    totalItems.value = response.data.total;
    totalPages.value = response.data.totalPages;

    // If the backend doesn't support sorting by automationByAgentq, sort locally
    if (sortField.value === 'automationByAgentq') {
      testResults.value.sort((a, b) => {
        const aValue = a.testCase?.automationByAgentq ? 1 : 0;
        const bValue = b.testCase?.automationByAgentq ? 1 : 0;

        return sortDirection.value === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      });
    }

    // Extract unique values for filter options
    extractFilterOptions();

  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch test results';
  } finally {
    loading.value = false;
  }
};

const extractFilterOptions = () => {
  const priorities = new Set<string>();
  const tags = new Map<string, string>();
  const types = new Set<string>();

  testResults.value.forEach(result => {
    if (result.testCase?.priority) {
      priorities.add(result.testCase.priority);
    }
    if (result.testCase?.tags) {
      result.testCase.tags.forEach(tag => {
        tags.set(tag.id, tag.name);
      });
    }
    if (result.testCase?.type) {
      types.add(result.testCase.type);
    }
  });

  priorityOptions.value = Array.from(priorities);
  tagOptions.value = Array.from(tags.entries()).map(([id, name]) => ({ id, name }));
  typeOptions.value = Array.from(types);
};

// AgentQ API key management

const fetchAgentQApiKey = async (): Promise<string | null> => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/api-keys`
    );
    
    // Check if we have any API keys
    if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
      console.error('No API keys found');
      return null;
    }
    
    // Find the AgentQ API key
    const agentqKey = response.data.find((key: any) => key.provider === 'agentq');
    
    if (!agentqKey || !agentqKey.apiKey) {
      console.error('No AgentQ API key found');
      return null;
    }
    
    console.log('AgentQ API key found');
    return agentqKey.apiKey;
  } catch (err) {
    console.error('Failed to fetch AgentQ API key:', err);
    return null;
  }
};

const fetchDastSummary = async () => {
  try {
    // Fetch DAST-specific summary data
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/dast-summary`
    );
    dastSummaryData.value = response.data;
  } catch (err: any) {
    console.warn('DAST summary not available, using mock data');
    // Mock data for demonstration
    dastSummaryData.value = {
      vulnerabilities: {
        critical: 2,
        high: 5,
        medium: 12,
        low: 8,
        falsePositive: 3
      },
      executionStatus: {
        new: 8,
        open: 12,
        inProgress: 5,
        fixedRemediated: 15,
        verifiedRetested: 3,
        closed: 7,
        falsePositive: 2,
        acceptedRisk: 1,
        duplicate: 1
      },
      testCoverage: {
        totalTestCases: 45,
        executedTestCases: 42,
        coveragePercentage: 93.3
      },
      securityMetrics: {
        authenticationTests: 8,
        authorizationTests: 12,
        inputValidationTests: 15,
        sessionManagementTests: 7
      }
    };
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchTestResults();
};

const handlePageChange = async (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    await fetchTestResults();
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
      tableContainer.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }
};



// Handle sorting
const handleSort = (field: string) => {
  if (sortField.value === field) {
    // Toggle direction if clicking the same field
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // Default to ascending for a new sort field
    sortField.value = field;
    sortDirection.value = 'asc';
  }
  currentPage.value = 1;
  fetchTestResults();
};

// Get sort icon based on current sort state
const getSortIcon = (field: string) => {
  if (sortField.value !== field) return '↕️';
  return sortDirection.value === 'asc' ? '↑' : '↓';
};

// Open filter modal
const openFilterModal = () => {
  // Copy current filters to temp filters
  tempFilters.value = {
    status: [...filters.value.status],
    priority: [...filters.value.priority],
    tagIds: [...filters.value.tagIds],
    type: [...filters.value.type]
  };
  showFilterModal.value = true;
};

// Handle apply filters from modal component
const handleApplyFilters = (newFilters: typeof filters.value) => {
  filters.value = newFilters;

  // Clear the legacy status filter if using advanced filters
  if (newFilters.status.length > 0) {
    filteredStatus.value = null;
  }

  currentPage.value = 1;
  fetchTestResults();
};

// Reset and apply filters
const resetAndApplyFilters = () => {
  filters.value = {
    status: [],
    priority: [],
    tagIds: [],
    type: []
  };
  tempFilters.value = {
    status: [],
    priority: [],
    tagIds: [],
    type: []
  };
  currentPage.value = 1;
  fetchTestResults();
};

// Get active filter count
const getActiveFilterCount = () => {
  let count = 0;
  if (filters.value.status.length > 0) count += filters.value.status.length;
  if (filters.value.priority.length > 0) count += filters.value.priority.length;
  if (filters.value.tagIds.length > 0) count += filters.value.tagIds.length;
  if (filters.value.type.length > 0) count += filters.value.type.length;
  return count;
};

// Pagination computed property
const paginationRange = computed(() => {
  const range: number[] = [];
  const maxVisiblePages = 5;
  let start = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
  let end = Math.min(totalPages.value, start + maxVisiblePages - 1);

  if (end - start + 1 < maxVisiblePages) {
    start = Math.max(1, end - maxVisiblePages + 1);
  }

  for (let i = start; i <= end; i++) {
    range.push(i);
  }

  return range;
});





// Bulk operations
const isTestResultSelected = (result: TestResult): boolean => {
  return selectedTestResults.value.some(selected => selected.id === result.id);
};

const toggleTestResult = (result: TestResult, event: Event): void => {
  event.stopPropagation();

  if (isTestResultSelected(result)) {
    selectedTestResults.value = selectedTestResults.value.filter(selected => selected.id !== result.id);
  } else {
    selectedTestResults.value.push(result);
  }
};

const toggleAllTestResults = (): void => {
  if (selectedTestResults.value.length === testResults.value.length) {
    // If all are selected, deselect all
    selectedTestResults.value = [];
  } else {
    // Otherwise, select all
    selectedTestResults.value = [...testResults.value];
  }
};

const clearSelection = (): void => {
  selectedTestResults.value = [];
};

// Test execution state
const runningTests = ref(new Map());
const testLogs = ref(new Map());

// Function to add test log
const addTestLog = (testId: string, message: string) => {
  if (!testLogs.value.has(testId)) {
    testLogs.value.set(testId, []);
  }
  const logs = testLogs.value.get(testId);
  logs.push(`${new Date().toLocaleTimeString()}: ${message}`);

  // Keep only last 10 logs
  if (logs.length > 10) {
    logs.shift();
  }
};

// Function to run automation test
const runAutomationTest = async (result: TestResult, event: Event) => {
  event.stopPropagation();

  // Prevent running if already running
  if (runningTests.value.has(result.id)) {
    return;
  }

  try {
    // Mark test as running
    runningTests.value.set(result.id, true);
    addTestLog(result.id, '🚀 Starting test automation...');

    addTestLog(result.id, '🔄 Connecting to DAST security test server...');

    // Fetch AgentQ API key
    const apiKey = await fetchAgentQApiKey();

    if (!apiKey) {
      throw new Error('No AgentQ API key found');
    }

    // Get the current user's JWT token from localStorage
    const authToken = localStorage.getItem('token');

    if (!authToken) {
      addTestLog(result.id, '⚠️ Warning: No authentication token found, test results may not be saved');
    }

    // Check if queue is busy before starting
    try {
      const queueResponse = await axios.get(`${(import.meta as any).env.VITE_WEBSOCKET_TESTRUN_SECURITY_URL}/api/queue/busy`);
      if (queueResponse.data.data.busy) {
        addTestLog(result.id, `⏳ Queue is busy - ${queueResponse.data.data.message}`);
        addTestLog(result.id, `📊 Active jobs: ${queueResponse.data.data.active}, Waiting: ${queueResponse.data.data.waiting}`);
      }
    } catch (err) {
      console.warn('Could not check queue status:', err);
    }

    // Connect to DAST Security TestRun WebSocket server with BullMQ integration
    const wsUrl = (import.meta as any).env.VITE_WEBSOCKET_TESTRUN_SECURITY_URL || 'ws://localhost:3024';

    // Create a direct WebSocket connection
    const ws = new WebSocket(wsUrl);

    // Set up event handlers for the BullMQ-enabled WebSocket
    ws.onopen = () => {
      addTestLog(result.id, '✅ Connected to DAST security test automation server');
      console.log('🔗 WebSocket connected to:', wsUrl);

      // Send authentication message with both API key and auth token
      const authMessage = {
        type: 'auth',
        token: apiKey,
        authToken: authToken // Send the user's JWT token
      };
      console.log('📤 Sending auth message:', { ...authMessage, token: '***', authToken: '***' });
      ws.send(JSON.stringify(authMessage));
    };

    ws.onclose = () => {
      addTestLog(result.id, '⚠️ Disconnected from test automation server');
      addTestLog(result.id, '🔄 Test may still be running in background - check status in a moment');

      setTimeout(async () => {
        try {
          await fetchTestResults();
          await fetchDastSummary();
          console.log('🔄 Refreshed test results after WebSocket disconnect');
        } catch (err) {
          console.error('Error refreshing after disconnect:', err);
        }
      }, 5000);
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      addTestLog(result.id, '❌ WebSocket connection error');
      addTestLog(result.id, '🔄 Test may still be running in background - check status in a moment');

      setTimeout(async () => {
        try {
          await fetchTestResults();
          await fetchDastSummary();
          console.log('🔄 Refreshed test results after WebSocket error');
        } catch (err) {
          console.error('Error refreshing after error:', err);
        }
      }, 5000);
    };

    ws.onmessage = async (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📥 Received WebSocket message:', data.type, data);

        switch (data.type) {
          case 'auth_success':
            addTestLog(result.id, '🔐 Authentication successful');

            // Fetch automation steps (same as TestRunDetail.vue)
            const stepsResponse = await axios.get(
              `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${result.testCase.tcId}/automation`
            );

            let automationSteps = [];
            if (stepsResponse.data && stepsResponse.data.steps) {
              automationSteps = stepsResponse.data.steps;
            } 
            
            // Convert multiple actions to execution steps (same logic as TestAutomation.vue)
            const executionSteps = convertToExecutionSteps(automationSteps);

            // Check if there are any execution steps
            if (executionSteps.length === 0) {
              addTestLog(result.id, '⚠️ No automation actions defined for this test case');
              addTestLog(result.id, '❌ Cannot run test without automation actions');
              runningTests.value.delete(result.id);
              ws.close();
              return;
            }

            addTestLog(result.id, `🔍 Converted ${automationSteps.length} automation steps to ${executionSteps.length} execution steps`);

            // Send DAST test execution request with all necessary information for BullMQ
            const executeMessage = {
              type: 'execute_test',
              token: apiKey,
              authToken: authToken, // Include the user's JWT token
              testCaseId: result.testCase.id,
              tcId: result.testCase.tcId.toString(),
              projectId: projectId,
              testRunId: testRunId, // Required for test runs
              steps: executionSteps,
              testCase: {
                title: result.testCase.title,
                precondition: result.testCase.precondition,
                expectation: result.testCase.expectation,
                projectId: projectId,
                type: 'security - dast' // Specify this is a DAST security test
              }
            };
            console.log('📤 Sending execute_test message:', {
              ...executeMessage,
              token: '***',
              authToken: '***',
              steps: `${executionSteps.length} steps`
            });
            ws.send(JSON.stringify(executeMessage));
            break;

          case 'auth_failed':
            addTestLog(result.id, `❌ Authentication failed: ${data.message || 'Invalid credentials'}`);
            runningTests.value.delete(result.id);
            ws.close();
            break;

          case 'test_queued':
            addTestLog(result.id, `📋 DAST security test queued at position ${data.position}`);
            if (data.queueStats) {
              addTestLog(result.id, `📊 Queue: ${data.queueStats.active} active, ${data.queueStats.waiting} waiting`);
            }
            addTestLog(result.id, data.message || 'Security test has been added to the queue');
            break;

          case 'test_started':
            addTestLog(result.id, '🔍 DAST security scan started');
            addTestLog(result.id, data.message || 'Security vulnerability scanning in progress...');
            break;

          case 'test_progress':
            if (data.step) {
              addTestLog(result.id, `📝 Step ${data.step}: ${data.message}`);
            } else {
              addTestLog(result.id, `🔄 ${data.message}`);
            }
            break;

          case 'vulnerability_found':
            addTestLog(result.id, `🚨 Security vulnerability detected: ${data.severity} - ${data.message}`);
            break;

          case 'test_complete':
            const statusMessage = data.status === 'passed' ? '✅ DAST security scan completed - No vulnerabilities found' :
                                 data.status === 'failed' ? '❌ DAST security scan completed - Vulnerabilities detected' :
                                 '⚠️ DAST security scan completed';
            addTestLog(result.id, statusMessage);

            // Refresh the UI after test completion
            try {
              await fetchTestResults();

              // Show success notification
              if (data.status === 'passed') {
                console.log('✅ DAST security test completed successfully - UI refreshed');
              } else {
                console.log('❌ DAST security test failed - UI refreshed');
              }

              // Auto-refresh the page after test completion
              setTimeout(() => {
                window.location.reload();
              }, 3000); // Refresh after 3 seconds

            } catch (err) {
              console.error('Error refreshing test results:', err);
            }

            // Mark test as completed
            runningTests.value.delete(result.id);

            // Close the WebSocket connection
            ws.close();
            break;

          case 'test_error':
            addTestLog(result.id, `❌ DAST security test error: ${data.message}`);
            runningTests.value.delete(result.id);
            ws.close();
            break;

          default:
            addTestLog(result.id, `📝 ${data.message || 'DAST security test update received'}`);
        }
      } catch (err) {
        console.error('Error parsing WebSocket message:', err);
        addTestLog(result.id, '⚠️ Received invalid message from server');
      }
    };

  } catch (err: any) {
    console.error('Error starting automation test:', err);
    addTestLog(result.id, '❌ Failed to start test automation');
    addTestLog(result.id, err.response?.data?.message || 'Unknown error occurred');
    runningTests.value.delete(result.id);
  }
};

// Check if test is running
const isTestRunning = (testId: string) => {
  return runningTests.value.has(testId);
};

// Get tooltip with logs for running test
const getTestRunTooltip = (testId: string) => {
  if (!testLogs.value.has(testId)) {
    return '';
  }

  return testLogs.value.get(testId).slice(-5).join('\n');
};

// Periodic status check for running tests
const startPeriodicStatusCheck = () => {
  setInterval(async () => {
    if (runningTests.value.size > 0) {
      try {
        await fetchTestResults();
        console.log('🔄 Periodic status check completed for DAST tests');
      } catch (err) {
        console.error('Error in periodic status check:', err);
      }
    }
  }, 10000); // Check every 10 seconds if there are running tests
};

const handleRowClick = (result: TestResult, event: Event): void => {
  // If the click was on the checkbox or its container, don't open the modal
  if ((event.target as HTMLElement).closest('.checkbox-column') ||
      (event.target as HTMLElement).closest('.edit-button')) {
    return;
  }

  selectedTestResult.value = result;
  showHistoryModal.value = true;
};

const openBulkUpdateModal = (): void => {
  if (selectedTestResults.value.length === 0) return;

  selectedTestResult.value = selectedTestResults.value[0];
  isBulkUpdate.value = true;
  showHistoryModal.value = true;
};

const closeHistoryModal = (): void => {
  const wasBulkUpdate = isBulkUpdate.value;

  showHistoryModal.value = false;
  isBulkUpdate.value = false;

  if (wasBulkUpdate) {
    selectedTestResults.value = [];
  }
};

onMounted(() => {
  fetchTestRun();
  fetchTestResults();
  fetchDastSummary();
  fetchAgentQApiKey();
  startPeriodicStatusCheck(); // Start periodic status checking for DAST tests
});
</script>

<template>
  <div class="test-run-dast-detail">
    <div class="header">
      <button class="back-button" @click="goBack">← Back to Test Runs</button>
      <div v-if="testRun" class="test-run-info">
        <div class="title-row">
          <h2>{{ testRun.name }}</h2>
          <span class="type-badge security">
            Security DAST
          </span>
        </div>
        <p class="description">{{ testRun.description }}</p>
        <div class="metadata">
          <span v-if="testRun.environment">Environment: {{ testRun.environment }}</span>
          <span v-if="testRun.build">Build: {{ testRun.build }}</span>
          <span v-if="testRun.release">Release: {{ testRun.release }}</span>
        </div>
      </div>
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <!-- DAST-specific Security Overview Section -->
    <div class="dast-security-overview">
      <h3>Security Assessment Overview</h3>
      
      <div class="security-metrics-grid">
        <!-- Vulnerability Distribution Chart -->
        <div class="chart-container">
          <h4>Vulnerability Distribution</h4>
          <div class="chart">
            <Pie :data="vulnerabilityChartData" :options="chartOptions" :height="200" />
          </div>
        </div>

        <!-- Security Metrics Summary -->
        <div class="security-summary">
          <div class="metric-card high">
            <span class="label">High</span>
            <span class="value">{{ dastSummaryData.vulnerabilities.high }}</span>
          </div>
          <div class="metric-card medium">
            <span class="label">Medium</span>
            <span class="value">{{ dastSummaryData.vulnerabilities.medium }}</span>
          </div>
          <div class="metric-card low">
            <span class="label">Low</span>
            <span class="value">{{ dastSummaryData.vulnerabilities.low }}</span>
          </div>
          <div class="metric-card false-positive">
            <span class="label">False Positive</span>
            <span class="value">{{ dastSummaryData.vulnerabilities.falsePositive }}</span>
          </div>
        </div>
      </div>

      <!-- Test Coverage Information -->
      <div class="scan-coverage">
        <h4>Test Coverage</h4>
        <div class="coverage-stats">
          <div class="coverage-item">
            <span class="label">Total Test Cases</span>
            <span class="value">{{ dastSummaryData.testCoverage.totalTestCases }}</span>
          </div>
          <div class="coverage-item">
            <span class="label">Executed Test Cases</span>
            <span class="value">{{ dastSummaryData.testCoverage.executedTestCases }}</span>
          </div>
          <div class="coverage-item">
            <span class="label">Coverage</span>
            <span class="value">{{ dastSummaryData.testCoverage.coveragePercentage }}%</span>
          </div>
        </div>
        <div class="coverage-bar">
          <div
            class="coverage-fill"
            :style="{ width: dastSummaryData.testCoverage.coveragePercentage + '%' }"
          ></div>
        </div>
      </div>

      <!-- Security Test Categories -->
      <div class="security-categories">
        <h4>Security Test Categories</h4>
        <div class="category-grid">
          <div class="category-item">
            <span class="category-label">Authentication</span>
            <span class="category-count">{{ dastSummaryData.securityMetrics.authenticationTests }}</span>
          </div>
          <div class="category-item">
            <span class="category-label">Authorization</span>
            <span class="category-count">{{ dastSummaryData.securityMetrics.authorizationTests }}</span>
          </div>
          <div class="category-item">
            <span class="category-label">Input Validation</span>
            <span class="category-count">{{ dastSummaryData.securityMetrics.inputValidationTests }}</span>
          </div>
          <div class="category-item">
            <span class="category-label">Session Management</span>
            <span class="category-count">{{ dastSummaryData.securityMetrics.sessionManagementTests }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Test Execution Status Overview -->
    <div class="test-execution-overview">
      <h3>Status Distribution</h3>
        <div class="execution-chart-container">
          <div class="execution-chart">
            <Bar :data="executionStatusChartData" :options="barChartOptions" :height="160" />
          </div>
      </div>
    </div>

    <!-- Test Results Section -->
    <div class="test-results-section">
      <div class="results-header">
        <h3>Security Test Results</h3>
        <div class="actions-container">
          <div v-if="selectedTestResults.length > 0" class="bulk-actions">
            <button class="bulk-update-button" @click="openBulkUpdateModal">
              Update {{ selectedTestResults.length }} Selected Results
            </button>
            <button class="clear-selection-button" @click="clearSelection">
              Clear Selection
            </button>
          </div>
          <div class="search-and-filter">
            <div class="search-box">
              <input
                type="text"
                v-model="searchQuery"
                placeholder="Search by ID, title, type, priority, platform..."
                @keyup.enter="handleSearch"
              />
              <button class="search-button" @click="handleSearch">
                Search
              </button>
            </div>
            <button
              @click="openFilterModal"
              class="filter-button"
              :class="{ active: getActiveFilterCount() > 0 }"
            >
              <span class="button-icon">🔍</span> Filter
              <span v-if="getActiveFilterCount() > 0" class="filter-badge">{{ getActiveFilterCount() }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Filter Modal Component -->
      <FilterTestResultsModal
        v-model="showFilterModal"
        :status-options="statusOptions"
        :priority-options="priorityOptions"
        :tag-options="tagOptions"
        :type-options="typeOptions"
        :initial-filters="tempFilters"
        @apply="handleApplyFilters"
      />

      <!-- Active Filters Component -->
      <ActiveTestResultFilters
        :filters="filters"
        :tag-options="tagOptions"
        @clear="resetAndApplyFilters"
      />

      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="checkbox-column">
                <input
                  type="checkbox"
                  :checked="selectedTestResults.length > 0 && selectedTestResults.length === testResults.length"
                  :indeterminate="selectedTestResults.length > 0 && selectedTestResults.length < testResults.length"
                  @click="toggleAllTestResults"
                  class="checkbox"
                />
              </th>
              <th @click="handleSort('tcId')" class="sortable-header">
                ID <span class="sort-icon">{{ getSortIcon('tcId') }}</span>
              </th>
              <th @click="handleSort('title')" class="sortable-header">
                Title <span class="sort-icon">{{ getSortIcon('title') }}</span>
              </th>
              <th @click="handleSort('priority')" class="sortable-header">
                Priority <span class="sort-icon">{{ getSortIcon('priority') }}</span>
              </th>
              <th @click="handleSort('status')" class="sortable-header">
                Status <span class="sort-icon">{{ getSortIcon('status') }}</span>
              </th>
              <th @click="handleSort('automationByAgentq')" class="sortable-header">
                Action <span class="sort-icon">{{ getSortIcon('automationByAgentq') }}</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="result in testResults"
              :key="result.id"
              class="test-result-row"
              @click.prevent="handleRowClick(result, $event)"
            >
              <td class="checkbox-column" @click.stop>
                <input
                  type="checkbox"
                  :checked="isTestResultSelected(result)"
                  @click="toggleTestResult(result, $event)"
                  class="checkbox"
                />
              </td>
              <td>TC-{{ result.testCase?.tcId }}</td>
              <td>{{ result.testCase?.title }}</td>
              <td>
                {{ result.testCase?.priority }}
              </td>
              <td>
                <span :class="['status-badge', result.status]">
                  {{ result?.status }}
                </span>
              </td>
              <td>
                <button
                  v-if="!isTestRunning(result.id) && result.testCase?.automationByAgentq && result.testCase?.type?.toLowerCase() === 'security - dast'"
                  class="edit-button"
                  @click="runAutomationTest(result, $event)"
                >
                  <span class="edit-icon">▶️</span> Run Test
                </button>
                <button
                  v-if="isTestRunning(result.id) && result.testCase?.automationByAgentq && result.testCase?.type?.toLowerCase() === 'security - dast'"
                  class="edit-button running"
                  disabled
                  :title="getTestRunTooltip(result.id)"
                >
                  <span class="edit-icon">⏳</span> Running...
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="pagination">
      <button
        class="page-button"
        :disabled="currentPage === 1"
        @click="handlePageChange(currentPage - 1)"
      >
        Previous
      </button>

      <div class="page-numbers">
        <button
          v-for="page in paginationRange"
          :key="page"
          class="page-number"
          :class="{ active: currentPage === page }"
          @click="handlePageChange(page)"
        >
          {{ page }}
        </button>
      </div>

      <button
        class="page-button"
        :disabled="currentPage === totalPages"
        @click="handlePageChange(currentPage + 1)"
      >
        Next
      </button>
    </div>

    <!-- DAST-specific Enhancement Placeholder -->
    <div class="dast-enhancement-placeholder">
      <div class="placeholder-content">
        <h4>🔒 Future DAST Enhancements</h4>
        <p>This area will be enhanced with DAST-specific features:</p>
        <ul>
          <li>Detailed vulnerability reports with CVSS scores</li>
          <li>Security risk assessments and impact analysis</li>
          <li>Remediation recommendations and code examples</li>
          <li>Compliance mapping (OWASP Top 10, CWE, etc.)</li>
          <li>Attack vector analysis and exploitation scenarios</li>
        </ul>
      </div>
    </div>

    <!-- Modals -->
    <TestCaseDastResultHistory
      v-if="showHistoryModal"
      :show="showHistoryModal"
      :project-id="projectId"
      :test-run-id="testRunId"
      :test-result-id="selectedTestResult?.id || ''"
      :test-case-title="selectedTestResult?.testCase?.title || ''"
      :test-case-type="selectedTestResult?.testCase?.type || ''"
      :tc-type="selectedTestResult?.testCase?.type || ''"
      :test-case-priority="selectedTestResult?.testCase?.priority || ''"
      :test-case-platform="selectedTestResult?.testCase?.platform || ''"
      :test-case-tags="selectedTestResult?.testCase?.tags || []"
      :test-case-precondition="selectedTestResult?.testCase?.precondition || ''"
      :test-case-steps="selectedTestResult?.testCase?.steps || ''"
      :test-case-expectation="selectedTestResult?.testCase?.expectation || ''"
      :tc-id="selectedTestResult?.testCase?.tcId || ''"
      :test-case-status="selectedTestResult?.status || ''"
      :test-case-notes="selectedTestResult?.notes || ''"
      :test-case-vulnerability-description="selectedTestResult?.vulnerabilityDescription || ''"
      :test-case-original-severity="selectedTestResult?.originalSeverity || ''"
      :test-case-adjusted-severity="selectedTestResult?.adjustedSeverity || ''"
      :test-case-affected-urls="selectedTestResult?.affectedUrls || ''"
      :test-case-request-response="selectedTestResult?.requestResponse || ''"
      :test-case-remediation-guidance="selectedTestResult?.remediationGuidance || ''"
      :test-case-updated-at="selectedTestResult?.updatedAt || ''"
      :test-case-created-at="selectedTestResult?.createdAt || ''"
      :is-bulk-update="isBulkUpdate"
      :selected-test-results="selectedTestResults"
      @close="closeHistoryModal"
      @updated="fetchTestResults(); fetchDastSummary();"
    />

    <ExportTestRunModal
      v-if="showExportModal"
      :test-run-id="testRunId"
      :project-id="projectId"
      @close="showExportModal = false"
    />
  </div>
</template>

<style lang="scss" scoped>
.test-run-dast-detail {
  min-height: calc(100vh - 120px);
}

.header {
  margin-bottom: 5px;

  .back-button {
    padding: 5px 10px;
    background: none;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    color: #4b5563;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: #edf2f7;
    }
  }

  .test-run-info {
    margin-top: 24px;

    .title-row {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 12px;
    }

    h2 {
      font-size: 28px;
      color: #1e293b;
      margin: 0;
    }

    .type-badge {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.security {
        background-color: #fff3e0;
        color: #f57c00;
        border: 1px solid #ffcc02;
      }
    }

    .description {
      color: #64748b;
      margin-bottom: 24px;
      line-height: 1.6;
    }

    .metadata {
      display: flex;
      gap: 16px;
      color: #4a5568;
      font-size: 14px;

      span {
        padding: 8px 16px;
        background-color: #e2e8f0;
        border-radius: 8px;
      }
    }
  }
}

.error-message {
  background-color: #fef2f2;
  color: #b91c1c;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #ef4444;
}

.dast-security-overview {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  h3 {
    font-size: 20px;
    color: #1e293b;
    margin-bottom: 24px;
    font-weight: 600;
  }

  h4 {
    font-size: 16px;
    color: #374151;
    margin-bottom: 16px;
    font-weight: 500;
  }
}

.security-metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-bottom: 32px;
}

.chart-container {
  .chart {
    height: 200px;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }
}

.security-summary {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;

  .metric-card {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    border-left: 4px solid;

    .label {
      font-size: 14px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .value {
      font-size: 24px;
      font-weight: 700;
    }

    &.critical {
      border-left-color: #dc2626;
      .label { color: #dc2626; }
      .value { color: #dc2626; }
    }

    &.high {
      border-left-color: #ef4444;
      .label { color: #ef4444; }
      .value { color: #ef4444; }
    }

    &.medium {
      border-left-color: #f59e0b;
      .label { color: #f59e0b; }
      .value { color: #f59e0b; }
    }

    &.low {
      border-left-color: #10b981;
      .label { color: #10b981; }
      .value { color: #10b981; }
    }

    &.false-positive {
      border-left-color: #6b7280;
      .label { color: #6b7280; }
      .value { color: #6b7280; }
    }
  }
}

.scan-coverage {
  margin-bottom: 32px;

  .coverage-stats {
    display: flex;
    gap: 24px;
    margin-bottom: 16px;

    .coverage-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .label {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
      }

      .value {
        font-size: 18px;
        color: #1e293b;
        font-weight: 600;
      }
    }
  }

  .coverage-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;

    .coverage-fill {
      height: 100%;
      background: linear-gradient(90deg, #10b981, #059669);
      transition: width 0.3s ease-in-out;
    }
  }
}

.security-categories {
  .category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .category-item {
      background-color: #f1f5f9;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid #e2e8f0;

      .category-label {
        font-size: 14px;
        color: #374151;
        font-weight: 500;
      }

      .category-count {
        font-size: 18px;
        color: #1e293b;
        font-weight: 600;
        background-color: #3b82f6;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        min-width: 24px;
        text-align: center;
      }
    }
  }
}

.test-execution-overview {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  h3 {
    font-size: 18px;
    color: #1e293b;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .execution-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 32px;
    align-items: start;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  }

  .execution-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;

    .summary-item {
      background-color: #f8fafc;
      border-radius: 8px;
      padding: 12px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      text-align: center;
      min-height: 80px;
      justify-content: center;

      .label {
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
        line-height: 1.3;
      }

      .value {
        font-size: 20px;
        font-weight: 700;
        color: #1e293b;
      }

      &.passed .value {
        color: #10b981;
      }

      &.failed .value {
        color: #ef4444;
      }

      &.untested .value {
        color: #6b7280;
      }
    }
  }

  .execution-chart-container {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 20px;

    h4 {
      font-size: 16px;
      color: #1e293b;
      margin-bottom: 16px;
      font-weight: 600;
      text-align: center;
    }

    .execution-chart {
      height: 160px;
      width: 100%;
    }
  }
}

.test-results-section {
  background-color: white;
  border-radius: 12px;
  padding: 10px;
  margin-top: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  h3 {
    font-size: 18px;
    color: #1e293b;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 16px;

    .actions-container {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;

      .bulk-actions {
        display: flex;
        gap: 8px;

        .bulk-update-button {
          background-color: #3b82f6;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          transition: background-color 0.2s;

          &:hover {
            background-color: #2563eb;
          }
        }

        .clear-selection-button {
          background-color: #6b7280;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          transition: background-color 0.2s;

          &:hover {
            background-color: #4b5563;
          }
        }
      }

      .search-and-filter {
        display: flex;
        gap: 12px;
        align-items: center;

        .search-box {
          display: flex;
          gap: 8px;

          input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            min-width: 300px;

            &:focus {
              outline: none;
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }

          .search-button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;

            &:hover {
              background-color: #2563eb;
            }
          }
        }

        .filter-button {
          background-color: #f3f4f6;
          color: #374151;
          border: 1px solid #d1d5db;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
          position: relative;
          transition: all 0.2s;

          &:hover {
            background-color: #e5e7eb;
          }

          &.active {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
          }

          .filter-badge {
            background-color: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.table-container {
  overflow-x: auto;

  table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      padding: 14px;
      text-align: left;
      border-bottom: 1px solid #e2e8f0;
    }

    th {
      background-color: #f0f4f8;
      font-weight: 500;
      color: #1e293b;

      &.sortable-header {
        cursor: pointer;
        user-select: none;
        transition: background-color 0.2s;

        &:hover {
          background-color: #e2e8f0;
        }

        .sort-icon {
          margin-left: 8px;
          font-size: 12px;
          opacity: 0.6;
        }
      }

      &.checkbox-column {
        width: 50px;
        text-align: center;
      }
    }

    tbody tr {
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8fafc;
      }

      &.test-result-row {
        cursor: pointer;
      }
    }

    .checkbox-column {
      width: 50px;
      text-align: center;

      .checkbox {
        cursor: pointer;
      }
    }

    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      text-transform: capitalize;

      &.passed {
        background-color: #dcfce7;
        color: #166534;
      }

      &.failed {
        background-color: #fef2f2;
        color: #dc2626;
      }

      &.blocked {
        background-color: #fef3c7;
        color: #d97706;
      }

      &.skipped {
        background-color: #dbeafe;
        color: #2563eb;
      }

      &.untested {
        background-color: #f3f4f6;
        color: #6b7280;
      }
    }

    .edit-button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: background-color 0.2s;

      &:hover:not(:disabled) {
        background-color: #2563eb;
      }

      &.running {
        background-color: #f59e0b;
        cursor: not-allowed;

        &:hover {
          background-color: #f59e0b;
        }
      }

      .edit-icon {
        font-size: 10px;
      }
    }
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
  padding: 20px 0;

  .page-button {
    padding: 8px 16px;
    border: 1px solid #d1d5db;
    background-color: white;
    color: #374151;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;

    &:hover:not(:disabled) {
      background-color: #f3f4f6;
      border-color: #9ca3af;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-numbers {
    display: flex;
    gap: 4px;

    .page-number {
      padding: 8px 12px;
      border: 1px solid #d1d5db;
      background-color: white;
      color: #374151;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      min-width: 40px;
      text-align: center;
      transition: all 0.2s;

      &:hover {
        background-color: #f3f4f6;
        border-color: #9ca3af;
      }

      &.active {
        background-color: #3b82f6;
        color: white;
        border-color: #3b82f6;
      }
    }

    .ellipsis {
      padding: 8px 4px;
      color: #6b7280;
      font-size: 14px;
    }
  }
}

.dast-enhancement-placeholder {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  margin-top: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  .placeholder-content {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    background-color: #f9fafb;
    max-width: 600px;
    margin: 0 auto;

    h4 {
      font-size: 18px;
      color: #374151;
      margin-bottom: 16px;
      font-weight: 600;
    }

    p {
      color: #6b7280;
      margin-bottom: 16px;
      font-size: 14px;
    }

    ul {
      text-align: left;
      color: #4b5563;
      font-size: 14px;
      line-height: 1.6;

      li {
        margin-bottom: 8px;
        position: relative;
        padding-left: 8px;

        &:before {
          content: "•";
          color: #f59e0b;
          font-weight: bold;
          position: absolute;
          left: 0;
        }
      }
    }
  }
}

.tags-container {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag-badge {
  display: inline-block;
  padding: 4px 8px;
  background-color: #e0f2f7;
  color: #0366d6;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
