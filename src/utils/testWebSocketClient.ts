interface TestStep {
  step: number;
  stepName: string;
  action?: string;
  target?: string;
  value?: string;
  prompt?: string;
  // New fields for multiple actions support
  originalStep?: number;
  actionIndex?: number;
  totalActionsInStep?: number;
  // File upload support
  fileUrl?: string;
  fileId?: string;
}

interface TestCase {
  title: string;
  precondition?: string;
  expectation?: string;
  projectId: string;
}

interface TestExecutionRequest {
  testCaseId: string;
  tcId: string;
  steps: TestStep[];
  testCase: TestCase;
  projectId: string;
}

interface TestWebSocketResponse {
  type: 'test_start' | 'test_output' | 'test_complete' | 'test_error' | 'error' | 'auth_success' | 'test_queued' | 'queue_status';
  message?: string;
  testCaseId?: string;
  tcId?: string;
  output?: string;
  isError?: boolean;
  status?: 'passed' | 'failed' | 'waiting' | 'active' | 'completed' | 'failed';
  stdout?: string;
  stderr?: string;
  // Queue-related properties
  jobId?: string;
  position?: number;
  queueStats?: {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    total: number;
  };
}

export class TestWebSocketClient {
  private ws: WebSocket | null = null;
  private connected: boolean = false;
  private apiKey: string;
  private userJwtToken: string | null = null;
  private clientId: string | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000;

  // Event handlers
  public onTestStart?: (data: TestWebSocketResponse) => void;
  public onTestOutput?: (data: TestWebSocketResponse) => void;
  public onTestComplete?: (data: TestWebSocketResponse) => void;
  public onTestError?: (data: TestWebSocketResponse) => void;
  public onError?: (error: string) => void;
  public onConnected?: () => void;
  public onDisconnected?: () => void;
  public onTestQueued?: (data: TestWebSocketResponse) => void;
  public onQueueStatus?: (data: TestWebSocketResponse) => void;

  constructor(apiKey: string, wsUrl: string = 'ws://localhost:3021', userJwtToken?: string, clientId?: string) {
    this.apiKey = apiKey;
    this.userJwtToken = userJwtToken || null;
    this.clientId = clientId || null;
    this.connect(wsUrl);
  }

  private connect(wsUrl: string): void {
    try {
      this.ws = new WebSocket(wsUrl);
      this.setupEventHandlers();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.handleReconnect(wsUrl);
    }
  }

  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.connected = true;
      this.reconnectAttempts = 0;
      
      // Authenticate immediately after connection
      this.authenticate();
      
      if (this.onConnected) {
        this.onConnected();
      }
    };

    this.ws.onmessage = (event) => {
      try {
        const data: TestWebSocketResponse = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.connected = false;
      
      if (this.onDisconnected) {
        this.onDisconnected();
      }
      
      // Attempt to reconnect
      this.handleReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      if (this.onError) {
        this.onError('WebSocket connection error');
      }
    };
  }

  private shouldFilterMessage(data: TestWebSocketResponse): boolean {
    // Don't filter auth_success and general error messages
    if (data.type === 'auth_success' || data.type === 'error') {
      return false;
    }

    // Filter queue-related and test-related messages by client ID
    if (data.type === 'queue_status' || data.type === 'test_queued' ||
        data.type === 'test_start' || data.type === 'test_output' ||
        data.type === 'test_complete' || data.type === 'test_error') {

      // If we have a client ID and the message has a jobId, they should match
      if (this.clientId && data.jobId) {
        return this.clientId !== data.jobId;
      }
    }

    // Don't filter if we can't determine ownership
    return false;
  }

  private handleMessage(data: TestWebSocketResponse): void {
    console.log(`📨 Received WebSocket message:`, data);
    console.log(`🆔 Current client ID: ${this.clientId}, Message jobId: ${data.jobId}`);

    // Filter messages by client ID to prevent cross-contamination between test cases
    if (this.shouldFilterMessage(data)) {
      console.log(`🚫 Filtered message for different client. Expected: ${this.clientId}, Received: ${data.jobId}`);
      return;
    }

    console.log(`✅ Processing message type: ${data.type}`);
    switch (data.type) {
      case 'auth_success':
        console.log('Authentication successful');
        break;

      case 'test_queued':
        console.log('Test queued successfully:', data);
        if (this.onTestQueued) {
          this.onTestQueued(data);
        }
        break;

      case 'queue_status':
        console.log('Queue status update:', data);
        if (this.onQueueStatus) {
          this.onQueueStatus(data);
        }
        break;

      case 'test_start':
        console.log('Test execution started');
        if (this.onTestStart) {
          this.onTestStart(data);
        }
        break;

      case 'test_output':
        if (this.onTestOutput) {
          this.onTestOutput(data);
        }
        break;

      case 'test_complete':
        console.log('🎯 Test execution completed:', data.status);
        console.log('🎯 Full test_complete data:', data);
        if (this.onTestComplete) {
          console.log('🎯 Calling onTestComplete handler...');
          this.onTestComplete(data);
        } else {
          console.warn('⚠️ No onTestComplete handler registered!');
        }
        break;

      case 'test_error':
      case 'error':
        console.error('Test execution error:', data.message);
        if (this.onTestError) {
          this.onTestError(data);
        }
        break;

      default:
        console.log('Unknown message type:', data.type);
    }
  }

  private authenticate(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    this.ws.send(JSON.stringify({
      type: 'auth',
      token: this.apiKey
    }));
  }

  private handleReconnect(wsUrl?: string): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      if (this.onError) {
        this.onError('Failed to reconnect to WebSocket server');
      }
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (wsUrl) {
        this.connect(wsUrl);
      }
    }, delay);
  }

  public executeTest(testData: TestExecutionRequest): void {
    console.log('Executing test - checking connection status...');

    // With BullMQ queue system, we should be more aggressive about trying to send the test
    // The backend will handle queuing properly
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('Connection is ready, sending test immediately...');
      this.sendTestExecution(testData);
      return;
    }

    console.log('Connection not ready, attempting to establish connection and queue test...');
    this.queueTestExecution(testData);
  }

  private sendTestExecution(testData: TestExecutionRequest): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('Cannot send test execution: WebSocket not connected');
      if (this.onError) {
        this.onError('WebSocket connection lost during test execution');
      }
      return;
    }

    this.ws.send(JSON.stringify({
      type: 'execute_test',
      token: this.apiKey,
      userJwtToken: this.userJwtToken, // Send user JWT token for backend authentication
      clientId: this.clientId, // Send unique client ID to prevent job interference
      ...testData
    }));
  }

  private queueTestExecution(testData: TestExecutionRequest): void {
    console.log('Queueing test execution - attempting to connect...');

    // With BullMQ, we should try to send the test even if connection seems busy
    // The backend queue will handle multiple users properly
    const maxRetries = 3;
    let retryCount = 0;

    const attemptExecution = () => {
      retryCount++;
      console.log(`Attempt ${retryCount}/${maxRetries} to execute test`);

      // If we have a connection that's open, try to send
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        console.log('Connection is open, sending test execution...');
        this.sendTestExecution(testData);
        return;
      }

      // If we have a connecting connection, wait for it
      if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
        console.log('Connection is connecting, waiting...');
        const onOpen = () => {
          console.log('Connection opened, sending test execution...');
          this.sendTestExecution(testData);
          this.ws?.removeEventListener('open', onOpen);
          this.ws?.removeEventListener('error', onError);
        };

        const onError = () => {
          console.log('Connection failed, retrying...');
          this.ws?.removeEventListener('open', onOpen);
          this.ws?.removeEventListener('error', onError);
          if (retryCount < maxRetries) {
            setTimeout(attemptExecution, 1000 * retryCount); // Progressive delay
          } else {
            this.handleExecutionFailure();
          }
        };

        this.ws.addEventListener('open', onOpen, { once: true });
        this.ws.addEventListener('error', onError, { once: true });
        return;
      }

      // No connection or connection is closed, try to create new one
      if (retryCount < maxRetries) {
        console.log('Creating new connection...');
        this.createNewConnectionForExecution(testData, retryCount);
      } else {
        this.handleExecutionFailure();
      }
    };

    attemptExecution();
  }

  private createNewConnectionForExecution(testData: TestExecutionRequest, retryCount: number): void {
    // Create a new WebSocket connection specifically for this test
    const wsUrl = 'ws://localhost:3021'; // Use the same URL as constructor

    try {
      const newWs = new WebSocket(wsUrl);

      // Set up message handler for the new connection to preserve onTestComplete handlers
      newWs.onmessage = (event) => {
        try {
          const data: TestWebSocketResponse = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      newWs.onopen = () => {
        console.log('New connection established for test execution');

        // Authenticate first
        newWs.send(JSON.stringify({
          type: 'auth',
          token: this.apiKey
        }));

        // Wait a moment for auth, then send test
        setTimeout(() => {
          if (newWs.readyState === WebSocket.OPEN) {
            newWs.send(JSON.stringify({
              type: 'execute_test',
              token: this.apiKey,
              userJwtToken: this.userJwtToken,
              clientId: this.clientId, // Send unique client ID to prevent job interference
              ...testData
            }));

            // Replace the old connection with this new one
            if (this.ws) {
              this.ws.close();
            }
            this.ws = newWs;
            console.log('🔄 WebSocket connection replaced, message handlers preserved');
          }
        }, 500);
      };

      // Set up close handler for the new connection
      newWs.onclose = () => {
        console.log('New WebSocket connection closed');
        this.connected = false;
        if (this.onDisconnected) {
          this.onDisconnected();
        }
      };

      newWs.onerror = (error) => {
        console.log(`Connection attempt ${retryCount} failed:`, error);
        if (this.onError) {
          this.onError('WebSocket connection error');
        }
        if (retryCount < 3) {
          setTimeout(() => {
            this.createNewConnectionForExecution(testData, retryCount + 1);
          }, 1000 * retryCount);
        } else {
          this.handleExecutionFailure();
        }
      };

    } catch (error) {
      console.error('Failed to create new WebSocket connection:', error);
      this.handleExecutionFailure();
    }
  }

  private handleExecutionFailure(): void {
    console.error('Failed to establish connection for test execution after all retries');
    if (this.onError) {
      this.onError('Connection timeout. The test server may be busy. Please try again in a moment.');
    }
  }



  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.connected = false;
  }

  public isConnected(): boolean {
    return this.connected && this.ws?.readyState === WebSocket.OPEN;
  }
}

export type { TestExecutionRequest, TestStep, TestCase, TestWebSocketResponse };
